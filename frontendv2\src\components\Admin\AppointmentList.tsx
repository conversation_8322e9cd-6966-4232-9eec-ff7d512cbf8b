import { useState } from 'react';
import { type AdminAppointment } from '../../utils/api';
import { formatTimeTo12Hour } from '../../utils/timeUtils';

// Extended admin appointment interface with payment proofs
interface ExtendedAdminAppointment extends AdminAppointment {
  totalPrice?: number;
  paymentProofs?: Array<{
    id: string;
    amount: number;
    paymentMethod: string;
    proofImage: string;
    status: 'pending' | 'verified' | 'rejected';
    notes?: string;
    createdAt: string;
  }>;
}
import Pagination from '../Pagination';

// Payment Proof Modal Component
const PaymentProofModal = ({ proof, isOpen, onClose }: { proof: any, isOpen: boolean, onClose: () => void }) => {
  if (!isOpen || !proof) return null;

  return (
    <div style={{
      position: 'fixed',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      backgroundColor: 'rgba(0, 0, 0, 0.8)',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      zIndex: 9999,
      padding: '1rem'
    }} onClick={onClose}>
      <div style={{
        backgroundColor: 'white',
        borderRadius: '0.75rem',
        padding: '1.5rem',
        maxWidth: '90vw',
        maxHeight: '90vh',
        overflow: 'auto',
        position: 'relative'
      }} onClick={(e) => e.stopPropagation()}>
        <button
          onClick={onClose}
          style={{
            position: 'absolute',
            top: '1rem',
            right: '1rem',
            background: 'none',
            border: 'none',
            fontSize: '1.5rem',
            cursor: 'pointer',
            color: '#666'
          }}
        >
          ×
        </button>

        <div style={{ marginBottom: '1rem' }}>
          <h3 style={{ margin: '0 0 0.5rem 0', fontSize: '1.25rem', fontWeight: 'bold' }}>
            Payment Proof Details
          </h3>
          <div style={{ fontSize: '0.875rem', color: '#666', marginBottom: '1rem' }}>
            <p><strong>Amount:</strong> ${proof.amount}</p>
            <p><strong>Method:</strong> {proof.paymentMethod}</p>
            <p><strong>Status:</strong> {proof.status}</p>
            <p><strong>Date:</strong> {new Date(proof.createdAt).toLocaleDateString()}</p>
            {proof.notes && <p><strong>Notes:</strong> {proof.notes}</p>}
          </div>
        </div>

        <div style={{ textAlign: 'center' }}>
          <img
            src={proof.proofImage}
            alt="Payment Proof"
            style={{
              maxWidth: '100%',
              maxHeight: '60vh',
              objectFit: 'contain',
              borderRadius: '0.5rem',
              boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)'
            }}
          />
        </div>
      </div>
    </div>
  );
};

interface AppointmentListProps {
  appointments: AdminAppointment[];
  loading: boolean;
  selectedAppointments: string[];
  onSelectionChange: (selected: string[]) => void;
  onStatusUpdate: (id: string, status: string) => void;
  onEdit: (appointment: AdminAppointment) => void;
  onView: (appointment: AdminAppointment) => void;
  onDelete: (id: string) => void;
  pagination: {
    page: number;
    limit: number;
    total: number;
    pages: number;
  };
  onPageChange: (page: number) => void;
}

export default function AppointmentList({
  appointments,
  loading,
  selectedAppointments,
  onSelectionChange,
  onStatusUpdate,
  onEdit,
  onView,
  onDelete,
  pagination,
  onPageChange
}: AppointmentListProps) {
  const [sortField, setSortField] = useState<string>('date');
  const [selectedProof, setSelectedProof] = useState<any>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);

  const handleViewProof = (proof: any) => {
    setSelectedProof(proof);
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
    setSelectedProof(null);
  };
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('desc');

  // Handle select all
  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      onSelectionChange(appointments.map(apt => apt.id));
    } else {
      onSelectionChange([]);
    }
  };

  // Handle individual selection
  const handleSelect = (appointmentId: string, checked: boolean) => {
    if (checked) {
      onSelectionChange([...selectedAppointments, appointmentId]);
    } else {
      onSelectionChange(selectedAppointments.filter(id => id !== appointmentId));
    }
  };

  // Get status badge class
  const getStatusBadgeClass = (status: string) => {
    switch (status) {
      case 'pending': return 'status-badge status-pending';
      case 'confirmed': return 'status-badge status-confirmed';
      case 'completed': return 'status-badge status-completed';
      case 'cancelled': return 'status-badge status-cancelled';
      default: return 'status-badge';
    }
  };

  // Format date
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  // Format time
  const formatTime = (timeString: string) => {
    return formatTimeTo12Hour(timeString);
  };

  // Handle sort
  const handleSort = (field: string) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('asc');
    }
  };

  const isAllSelected = appointments.length > 0 && selectedAppointments.length === appointments.length;
  const isPartiallySelected = selectedAppointments.length > 0 && selectedAppointments.length < appointments.length;

  if (loading) {
    return (
      <div className="appointment-list-loading">
        <div className="loading-spinner"></div>
        <p>Loading appointments...</p>
      </div>
    );
  }

  if (appointments.length === 0) {
    return (
      <div className="appointment-list-empty">
        <div className="empty-state">
          <div className="empty-icon">📅</div>
          <h3>No appointments found</h3>
          <p>No appointments match your current filters.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="appointment-list">
      <div className="appointment-table-container" style={{
        overflowX: 'auto',
        width: '100%',
        borderRadius: '12px',
        boxShadow: '0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)',
        backgroundColor: 'white',
        border: '1px solid #e5e7eb'
      }}>
        <table className="appointment-table" style={{
          minWidth: '1600px',
          width: '100%',
          tableLayout: 'fixed',
          borderCollapse: 'collapse'
        }}>
          <thead>
            <tr>
              <th className="checkbox-column" style={{
                width: '50px',
                textAlign: 'center',
                padding: '12px 8px',
                borderBottom: '2px solid #e5e7eb'
              }}>
                <input
                  type="checkbox"
                  checked={isAllSelected}
                  ref={input => {
                    if (input) input.indeterminate = isPartiallySelected;
                  }}
                  onChange={(e) => handleSelectAll(e.target.checked)}
                />
              </th>
              <th
                className={`sortable ${sortField === 'customerInfo.name' ? `sorted-${sortDirection}` : ''}`}
                onClick={() => handleSort('customerInfo.name')}
                style={{
                  width: '220px',
                  textAlign: 'left',
                  padding: '12px 16px',
                  borderBottom: '2px solid #e5e7eb',
                  fontWeight: '600',
                  color: '#374151'
                }}
              >
                Customer
              </th>
              <th
                className={`sortable ${sortField === 'service.name' ? `sorted-${sortDirection}` : ''}`}
                onClick={() => handleSort('service.name')}
                style={{
                  width: '250px',
                  textAlign: 'left',
                  padding: '12px 16px',
                  borderBottom: '2px solid #e5e7eb',
                  fontWeight: '600',
                  color: '#374151'
                }}
              >
                Service
              </th>
              <th
                className={`sortable ${sortField === 'date' ? `sorted-${sortDirection}` : ''}`}
                onClick={() => handleSort('date')}
                style={{
                  width: '160px',
                  textAlign: 'left',
                  padding: '12px 16px',
                  borderBottom: '2px solid #e5e7eb',
                  fontWeight: '600',
                  color: '#374151'
                }}
              >
                Date & Time
              </th>
              <th
                className={`sortable ${sortField === 'status' ? `sorted-${sortDirection}` : ''}`}
                onClick={() => handleSort('status')}
                style={{
                  width: '120px',
                  textAlign: 'center',
                  padding: '12px 16px',
                  borderBottom: '2px solid #e5e7eb',
                  fontWeight: '600',
                  color: '#374151'
                }}
              >
                Status
              </th>
              <th style={{
                width: '100px',
                textAlign: 'right',
                padding: '12px 16px',
                borderBottom: '2px solid #e5e7eb',
                fontWeight: '600',
                color: '#374151'
              }}>
                Price
              </th>
              <th style={{
                width: '320px',
                textAlign: 'center',
                padding: '12px 16px',
                borderBottom: '2px solid #e5e7eb',
                fontWeight: '600',
                color: '#374151'
              }}>
                Payment Proof
              </th>
              <th style={{
                width: '180px',
                textAlign: 'center',
                padding: '12px 16px',
                borderBottom: '2px solid #e5e7eb',
                fontWeight: '600',
                color: '#374151'
              }}>
                Actions
              </th>
            </tr>
          </thead>
          <tbody>
            {appointments.map((appointment) => (
              <tr key={appointment.id} className="appointment-row" style={{
                borderBottom: '1px solid #f3f4f6',
                backgroundColor: selectedAppointments.includes(appointment.id) ? '#f0f9ff' : 'white',
                transition: 'background-color 0.2s ease'
              }}
              onMouseEnter={(e) => {
                if (!selectedAppointments.includes(appointment.id)) {
                  e.currentTarget.style.backgroundColor = '#f9fafb';
                }
              }}
              onMouseLeave={(e) => {
                if (!selectedAppointments.includes(appointment.id)) {
                  e.currentTarget.style.backgroundColor = 'white';
                }
              }}>
                <td className="checkbox-column" style={{
                  textAlign: 'center',
                  padding: '16px 8px',
                  verticalAlign: 'middle'
                }}>
                  <input
                    type="checkbox"
                    checked={selectedAppointments.includes(appointment.id)}
                    onChange={(e) => handleSelect(appointment.id, e.target.checked)}
                  />
                </td>
                <td className="customer-column" style={{
                  padding: '16px',
                  verticalAlign: 'middle'
                }}>
                  <div className="customer-info">
                    <div className="customer-name" style={{
                      fontWeight: '600',
                      color: '#111827',
                      marginBottom: '4px'
                    }}>
                      {appointment.customerInfo.name ||
                       (appointment.user ? appointment.user.name : 'Guest')}
                    </div>
                    <div className="customer-email" style={{
                      fontSize: '0.875rem',
                      color: '#6b7280',
                      marginBottom: '2px'
                    }}>
                      {appointment.customerInfo.email || appointment.user?.email}
                    </div>
                    {(appointment.customerInfo.phone || appointment.user?.phone) && (
                      <div className="customer-phone" style={{
                        fontSize: '0.875rem',
                        color: '#6b7280'
                      }}>
                        {appointment.customerInfo.phone || appointment.user?.phone}
                      </div>
                    )}
                  </div>
                </td>
                <td className="service-column" style={{
                  padding: '16px',
                  verticalAlign: 'middle'
                }}>
                  <div className="service-info">
                    <div className="service-name" style={{
                      fontWeight: '600',
                      color: '#111827',
                      marginBottom: '4px'
                    }}>
                      {appointment.service?.name || 'Service Unavailable'}
                    </div>
                    <div className="service-category" style={{
                      fontSize: '0.875rem',
                      color: '#6b7280',
                      marginBottom: '2px'
                    }}>
                      {appointment.service?.category || 'N/A'}
                    </div>
                    <div className="service-duration" style={{
                      fontSize: '0.875rem',
                      color: '#6b7280'
                    }}>
                      {appointment.service?.duration ? `${appointment.service.duration} min` : 'N/A'}
                    </div>
                  </div>
                </td>
                <td className="datetime-column" style={{
                  padding: '16px',
                  verticalAlign: 'middle'
                }}>
                  <div className="datetime-info">
                    <div className="date" style={{
                      fontWeight: '600',
                      color: '#111827',
                      marginBottom: '4px'
                    }}>
                      {formatDate(appointment.date)}
                    </div>
                    <div className="time" style={{
                      fontSize: '0.875rem',
                      color: '#6b7280'
                    }}>
                      {formatTime(appointment.time)}
                    </div>
                  </div>
                </td>
                <td className="status-column" style={{
                  padding: '16px',
                  verticalAlign: 'middle',
                  textAlign: 'center'
                }}>
                  <select
                    className={getStatusBadgeClass(appointment.status)}
                    value={appointment.status}
                    onChange={(e) => onStatusUpdate(appointment.id, e.target.value)}
                    style={{
                      padding: '6px 12px',
                      borderRadius: '20px',
                      fontSize: '0.75rem',
                      fontWeight: '600',
                      border: 'none',
                      outline: 'none',
                      cursor: 'pointer',
                      backgroundColor: appointment.status === 'confirmed' ? '#dcfce7' :
                                     appointment.status === 'pending' ? '#fef3c7' :
                                     appointment.status === 'cancelled' ? '#fef2f2' :
                                     appointment.status === 'completed' ? '#dbeafe' : '#f3f4f6',
                      color: appointment.status === 'confirmed' ? '#166534' :
                             appointment.status === 'pending' ? '#92400e' :
                             appointment.status === 'cancelled' ? '#dc2626' :
                             appointment.status === 'completed' ? '#1e40af' : '#6b7280'
                    }}
                  >
                    <option value="pending">Pending</option>
                    <option value="confirmed">Confirmed</option>
                    <option value="completed">Completed</option>
                    <option value="cancelled">Cancelled</option>
                  </select>
                </td>
                <td className="price-column" style={{
                  padding: '16px',
                  verticalAlign: 'middle',
                  textAlign: 'right'
                }}>
                  <div className="price" style={{
                    fontWeight: '600',
                    color: '#059669',
                    fontSize: '1rem'
                  }}>
                    ${appointment.service?.price || '0'}
                  </div>
                </td>
                <td className="payment-proof-column" style={{ minWidth: '320px', maxWidth: '320px' }}>
                  <div className="payment-proof-info">
                    {(appointment as ExtendedAdminAppointment).paymentProofs && (appointment as ExtendedAdminAppointment).paymentProofs!.length > 0 ? (
                      <div className="payment-proofs" style={{ display: 'flex', flexDirection: 'column', gap: '4px' }}>
                        {(appointment as ExtendedAdminAppointment).paymentProofs!.map((proof: any, index: number) => (
                          <div key={proof.id || index} className="payment-proof-item">
                            <button
                              onClick={() => handleViewProof(proof)}
                              className="payment-proof-link"
                              title={`View payment proof - ${proof.paymentMethod} - $${proof.amount}`}
                              style={{
                                display: 'block',
                                padding: '6px 8px',
                                backgroundColor: proof.status === 'verified' ? '#dcfce7' : proof.status === 'rejected' ? '#fef2f2' : '#fef3c7',
                                color: proof.status === 'verified' ? '#166534' : proof.status === 'rejected' ? '#dc2626' : '#92400e',
                                borderRadius: '4px',
                                fontSize: '11px',
                                border: 'none',
                                cursor: 'pointer',
                                fontWeight: '500',
                                width: '100%',
                                textAlign: 'left',
                                marginBottom: '2px',
                                whiteSpace: 'nowrap',
                                overflow: 'hidden',
                                textOverflow: 'ellipsis'
                              }}
                            >
                              📄 {proof.paymentMethod} ${proof.amount}
                            </button>
                            <div style={{
                              fontSize: '9px',
                              color: '#6b7280',
                              textAlign: 'center',
                              textTransform: 'uppercase',
                              fontWeight: '500'
                            }}>
                              {proof.status}
                            </div>
                          </div>
                        ))}
                      </div>
                    ) : (
                      <span className="no-payment-proof" style={{
                        color: '#9ca3af',
                        fontSize: '12px',
                        fontStyle: 'italic'
                      }}>
                        No payment proof
                      </span>
                    )}
                  </div>
                </td>
                <td className="actions-column" style={{
                  padding: '16px',
                  verticalAlign: 'middle',
                  textAlign: 'center'
                }}>
                  <div className="action-buttons" style={{
                    display: 'flex',
                    gap: '8px',
                    justifyContent: 'center',
                    alignItems: 'center'
                  }}>
                    <button
                      className="btn btn-sm btn-outline"
                      onClick={() => onView(appointment)}
                      title="View Details"
                      style={{
                        padding: '6px 8px',
                        border: '1px solid #d1d5db',
                        borderRadius: '6px',
                        backgroundColor: 'white',
                        cursor: 'pointer',
                        fontSize: '14px',
                        transition: 'all 0.2s ease'
                      }}
                      onMouseEnter={(e) => {
                        e.currentTarget.style.backgroundColor = '#f3f4f6';
                        e.currentTarget.style.borderColor = '#9ca3af';
                      }}
                      onMouseLeave={(e) => {
                        e.currentTarget.style.backgroundColor = 'white';
                        e.currentTarget.style.borderColor = '#d1d5db';
                      }}
                    >
                      👁️
                    </button>
                    <button
                      className="btn btn-sm btn-outline"
                      onClick={() => onEdit(appointment)}
                      title="Edit"
                      style={{
                        padding: '6px 8px',
                        border: '1px solid #d1d5db',
                        borderRadius: '6px',
                        backgroundColor: 'white',
                        cursor: 'pointer',
                        fontSize: '14px',
                        transition: 'all 0.2s ease'
                      }}
                      onMouseEnter={(e) => {
                        e.currentTarget.style.backgroundColor = '#f3f4f6';
                        e.currentTarget.style.borderColor = '#9ca3af';
                      }}
                      onMouseLeave={(e) => {
                        e.currentTarget.style.backgroundColor = 'white';
                        e.currentTarget.style.borderColor = '#d1d5db';
                      }}
                    >
                      ✏️
                    </button>
                    <button
                      className="btn btn-sm btn-danger"
                      onClick={() => onDelete(appointment.id)}
                      title="Delete"
                      style={{
                        padding: '6px 8px',
                        border: '1px solid #dc2626',
                        borderRadius: '6px',
                        backgroundColor: '#fef2f2',
                        color: '#dc2626',
                        cursor: 'pointer',
                        fontSize: '14px',
                        transition: 'all 0.2s ease'
                      }}
                      onMouseEnter={(e) => {
                        e.currentTarget.style.backgroundColor = '#dc2626';
                        e.currentTarget.style.color = 'white';
                      }}
                      onMouseLeave={(e) => {
                        e.currentTarget.style.backgroundColor = '#fef2f2';
                        e.currentTarget.style.color = '#dc2626';
                      }}
                    >
                      🗑️
                    </button>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {pagination.pages > 1 && (
        <Pagination
          currentPage={pagination.page}
          totalPages={pagination.pages}
          totalItems={pagination.total}
          itemsPerPage={pagination.limit}
          onPageChange={onPageChange}
        />
      )}

      {/* Payment Proof Modal */}
      <PaymentProofModal
        proof={selectedProof}
        isOpen={isModalOpen}
        onClose={handleCloseModal}
      />
    </div>
  );
}
