// React import removed - not needed for this component
import { useLocation, useNavigate } from 'react-router-dom';
import { formatTimeTo12Hour } from '../utils/timeUtils';

interface AppointmentData {
  id: string;
  userId?: string;
  serviceId: string;
  serviceName: string;
  servicePrice: number;
  date: string;
  time?: string;
  status: string;
  customerInfo?: {
    name: string;
    email: string;
    phone: string;
  };
  totalPrice: number;
  addOns?: any[];
  notes?: string;
  paymentProofs?: Array<{
    id: string;
    amount: number;
    paymentMethod: string;
    proofImage: string;
    status: string;
    notes?: string;
    createdAt: string;
  }>;
  paymentStatus?: string;
  createdAt: string;
}

export default function AppointmentSuccess() {
  const location = useLocation();
  const navigate = useNavigate();
  const appointmentData = location.state?.appointmentData as AppointmentData;

  if (!appointmentData) {
    return (
      <div className="app">
        <header className="header">
          <div className="header-content">
            <div className="header-left">
              <h1>MicroLocs</h1>
            </div>
            <div className="auth-links">
              <button onClick={() => navigate('/')} className="auth-link">HOME</button>
            </div>
          </div>
        </header>

        <main className="main-content" style={{ display: 'flex', alignItems: 'center', justifyContent: 'center', minHeight: '60vh' }}>
          <div className="appointment-card" style={{ textAlign: 'center', maxWidth: '400px' }}>
            <h2 style={{ fontSize: '1.5rem', fontWeight: 'bold', color: '#333', marginBottom: '1rem' }}>
              No Appointment Data
            </h2>
            <p style={{ color: '#666', marginBottom: '2rem' }}>
              We couldn't find your appointment information.
            </p>
            <button
              onClick={() => navigate('/')}
              className="continue-button"
              style={{
                backgroundColor: '#2c5530',
                color: 'white',
                padding: '0.75rem 1.5rem',
                borderRadius: '8px',
                border: 'none',
                fontSize: '1rem',
                fontWeight: '600',
                cursor: 'pointer'
              }}
            >
              Go Home
            </button>
          </div>
        </main>
      </div>
    );
  }

  const formatDate = (dateString: string) => {
    try {
      // Handle ISO date format from API
      const date = new Date(dateString);

      // Check if date is valid
      if (isNaN(date.getTime())) {
        return 'Invalid Date';
      }

      return date.toLocaleDateString('en-US', {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      });
    } catch (error) {
      console.warn('Error formatting date:', error);
      return 'Invalid Date';
    }
  };

  // Use centralized time formatting utility
  const formatTime = formatTimeTo12Hour;

  return (
    <div className="app">
      <header className="header">
        <div className="header-content">
          <div className="header-left">
            <h1>MicroLocs</h1>
          </div>
          <div className="auth-links">
            <button onClick={() => navigate('/')} className="auth-link">HOME</button>
          </div>
        </div>
      </header>

      <main className="main-content">
        {/* Success Header */}
        <div style={{ textAlign: 'center', marginBottom: '2rem' }}>
          <div style={{
            width: '80px',
            height: '80px',
            backgroundColor: '#d4edda',
            borderRadius: '50%',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            margin: '0 auto 1rem',
            border: '3px solid #28a745'
          }}>
            <svg style={{ width: '40px', height: '40px', color: '#28a745' }} fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={3} d="M5 13l4 4L19 7" />
            </svg>
          </div>
          <h1 style={{ fontSize: '2.5rem', fontWeight: 'bold', color: '#2c5530', marginBottom: '0.5rem' }}>
            Appointment Confirmed!
          </h1>
          <p style={{ color: '#666', fontSize: '1.1rem' }}>Your appointment has been successfully booked.</p>
        </div>

        {/* Appointment Details Card */}
        <div className="appointment-card" style={{ marginBottom: '2rem' }}>
          <div className="appointment-header">
            <h2 style={{ fontSize: '1.5rem', fontWeight: '600', color: '#2c5530', marginBottom: '1rem' }}>
              Appointment Details
            </h2>
          </div>

          <div className="appointment-details">
            {/* Service */}
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', marginBottom: '1rem' }}>
              <div>
                <h4 style={{ fontSize: '1.1rem', fontWeight: '600', color: '#333', marginBottom: '0.25rem' }}>
                  {appointmentData.serviceName}
                </h4>
                <p style={{ fontSize: '0.9rem', color: '#666' }}>Service</p>
              </div>
              <p className="appointment-price" style={{ fontSize: '1.1rem', fontWeight: '600', color: '#2c5530' }}>
                ${appointmentData.servicePrice}
              </p>
            </div>

            {/* Date & Time */}
            <div style={{ borderTop: '1px solid #eee', paddingTop: '1rem', marginBottom: '1rem' }}>
              <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(150px, 1fr))', gap: '1rem' }}>
                <div>
                  <p className="appointment-datetime" style={{ fontSize: '1rem', fontWeight: '600', color: '#333', marginBottom: '0.25rem' }}>
                    {formatDate(appointmentData.date)}
                  </p>
                  <p style={{ fontSize: '0.9rem', color: '#666' }}>Date</p>
                </div>
                <div>
                  <p className="appointment-datetime" style={{ fontSize: '1rem', fontWeight: '600', color: '#333', marginBottom: '0.25rem' }}>
                    {formatTime(appointmentData.time)}
                  </p>
                  <p style={{ fontSize: '0.9rem', color: '#666' }}>Time</p>
                </div>
                <div>
                  <p style={{
                    fontSize: '1rem',
                    fontWeight: '600',
                    color: appointmentData.status === 'confirmed' ? '#28a745' :
                           appointmentData.status === 'pending' ? '#ffc107' : '#6c757d',
                    marginBottom: '0.25rem'
                  }}>
                    {(appointmentData.status || 'PENDING').toUpperCase()}
                  </p>
                  <p style={{ fontSize: '0.9rem', color: '#666' }}>Status</p>
                </div>
                <div>
                  <p style={{
                    fontSize: '1rem',
                    fontWeight: '600',
                    color: appointmentData.paymentStatus === 'paid' ? '#28a745' :
                           appointmentData.paymentStatus === 'pending' ? '#ffc107' : '#6c757d',
                    marginBottom: '0.25rem'
                  }}>
                    {(appointmentData.paymentStatus || 'PENDING').toUpperCase()}
                  </p>
                  <p style={{ fontSize: '0.9rem', color: '#666' }}>Payment</p>
                </div>
              </div>
            </div>

            {/* Customer Info */}
            <div style={{ borderTop: '1px solid #eee', paddingTop: '1rem', marginBottom: '1rem' }}>
              <h3 style={{ fontSize: '1rem', fontWeight: '600', color: '#333', marginBottom: '0.5rem' }}>
                Customer Information
              </h3>
              <div style={{ display: 'flex', flexDirection: 'column', gap: '0.25rem' }}>
                <p style={{ color: '#555' }}>{appointmentData.customerInfo?.name || 'N/A'}</p>
                <p style={{ color: '#555' }}>{appointmentData.customerInfo?.email || 'N/A'}</p>
                <p style={{ color: '#555' }}>{appointmentData.customerInfo?.phone || 'N/A'}</p>
              </div>
            </div>

            {/* Add-ons */}
            {appointmentData.addOns && appointmentData.addOns.length > 0 && (
              <div style={{ borderTop: '1px solid #eee', paddingTop: '1rem', marginBottom: '1rem' }}>
                <h3 style={{ fontSize: '1rem', fontWeight: '600', color: '#333', marginBottom: '0.5rem' }}>Add-ons</h3>
                <div style={{ display: 'flex', flexDirection: 'column', gap: '0.25rem' }}>
                  {appointmentData.addOns.map((addOn: any, index: number) => (
                    <div key={index} style={{ display: 'flex', justifyContent: 'space-between' }}>
                      <span style={{ color: '#555' }}>{addOn.name}</span>
                      <span style={{ color: '#555' }}>${addOn.price}</span>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Payment Proof */}
            {appointmentData.paymentProofs && appointmentData.paymentProofs.length > 0 && (
              <div style={{ borderTop: '1px solid #eee', paddingTop: '1rem', marginBottom: '1rem' }}>
                <h3 style={{ fontSize: '1rem', fontWeight: '600', color: '#333', marginBottom: '0.5rem' }}>Payment Proof</h3>
                <div style={{ display: 'flex', flexDirection: 'column', gap: '0.5rem' }}>
                  {appointmentData.paymentProofs.map((proof, index) => (
                    <div key={proof.id || index} style={{
                      padding: '0.75rem',
                      backgroundColor: '#f8f9fa',
                      borderRadius: '6px',
                      border: '1px solid #e9ecef'
                    }}>
                      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '0.5rem' }}>
                        <span style={{ fontWeight: '500', color: '#333' }}>
                          {(proof.paymentMethod || 'UNKNOWN').toUpperCase()} - ${proof.amount}
                        </span>
                        <span style={{
                          padding: '0.25rem 0.5rem',
                          borderRadius: '4px',
                          fontSize: '0.75rem',
                          fontWeight: '500',
                          backgroundColor: proof.status === 'verified' ? '#d4edda' :
                                         proof.status === 'rejected' ? '#f8d7da' : '#fff3cd',
                          color: proof.status === 'verified' ? '#155724' :
                                 proof.status === 'rejected' ? '#721c24' : '#856404'
                        }}>
                          {(proof.status || 'PENDING').toUpperCase()}
                        </span>
                      </div>
                      <p style={{ fontSize: '0.8rem', color: '#666', margin: 0 }}>
                        Submitted: {new Date(proof.createdAt).toLocaleDateString()}
                      </p>
                      {proof.notes && (
                        <p style={{ fontSize: '0.8rem', color: '#666', margin: '0.25rem 0 0 0' }}>
                          {proof.notes}
                        </p>
                      )}
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Notes */}
            {appointmentData.notes && (
              <div className="appointment-notes" style={{ borderTop: '1px solid #eee', paddingTop: '1rem', marginBottom: '1rem' }}>
                <h3 style={{ fontSize: '1rem', fontWeight: '600', color: '#333', marginBottom: '0.5rem' }}>Notes</h3>
                <p style={{ color: '#555' }}>{appointmentData.notes}</p>
              </div>
            )}

            {/* Total */}
            <div style={{ borderTop: '2px solid #2c5530', paddingTop: '1rem' }}>
              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <p style={{ fontSize: '1.25rem', fontWeight: '600', color: '#333' }}>Total</p>
                <p style={{ fontSize: '1.5rem', fontWeight: 'bold', color: '#2c5530' }}>${appointmentData.totalPrice}</p>
              </div>
            </div>
          </div>
        </div>

        {/* Confirmation ID */}
        <div style={{
          backgroundColor: '#e3f2fd',
          borderRadius: '8px',
          padding: '1rem',
          marginBottom: '2rem',
          border: '1px solid #bbdefb'
        }}>
          <p style={{ fontSize: '0.9rem', color: '#1565c0', marginBottom: '0.5rem' }}>
            <span style={{ fontWeight: '600' }}>Confirmation ID:</span> {appointmentData.id}
          </p>
          <p style={{ fontSize: '0.8rem', color: '#1976d2' }}>
            Please save this ID for your records. You'll receive a confirmation email shortly.
          </p>
        </div>

        {/* Action Buttons */}
        <div style={{
          display: 'flex',
          flexDirection: window.innerWidth > 768 ? 'row' : 'column',
          gap: '1rem',
          marginBottom: '2rem'
        }}>
          <button
            onClick={() => navigate('/')}
            className="continue-button"
            style={{
              backgroundColor: '#2c5530',
              color: 'white',
              padding: '1rem 2rem',
              borderRadius: '8px',
              border: 'none',
              fontSize: '1rem',
              fontWeight: '600',
              cursor: 'pointer',
              transition: 'background-color 0.3s'
            }}
            onMouseOver={(e) => (e.target as HTMLButtonElement).style.backgroundColor = '#1e3a21'}
            onMouseOut={(e) => (e.target as HTMLButtonElement).style.backgroundColor = '#2c5530'}
          >
            Book Another Appointment
          </button>
          <button
            onClick={() => window.print()}
            style={{
              backgroundColor: '#f8f9fa',
              color: '#333',
              padding: '1rem 2rem',
              borderRadius: '8px',
              border: '1px solid #dee2e6',
              fontSize: '1rem',
              fontWeight: '600',
              cursor: 'pointer',
              transition: 'background-color 0.3s'
            }}
            onMouseOver={(e) => (e.target as HTMLButtonElement).style.backgroundColor = '#e9ecef'}
            onMouseOut={(e) => (e.target as HTMLButtonElement).style.backgroundColor = '#f8f9fa'}
          >
            Print Details
          </button>
        </div>


      </main>
    </div>
  );
}
