import { useState } from 'react'
import DatePicker from 'react-datepicker'
import 'react-datepicker/dist/react-datepicker.css';
import { useToast } from '../contexts/ToastContext';
import { API_CONFIG } from '../utils/config';
import { formatTimeSlot } from '../utils/timeUtils';
import Lightbox from "yet-another-react-lightbox";
import "yet-another-react-lightbox/styles.css";

// Utility function to format date consistently
const formatDateForAPI = (date: Date): string => {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  return `${year}-${month}-${day}`;
};

interface AddOnService {
  id: string;
  name: string;
  duration: number;
  price: number;
}

interface BookingState {
  selectedService: any;
  selectedAddOns: AddOnService[];
  selectedDate: string;
  selectedTime: string;
  step: 'services' | 'datetime' | 'details' | 'checkout';
}

interface DateTimeSelectionProps {
  booking: BookingState;
  onBack: () => void;
  onSelect: (date: string, time: string) => void;
  addOnServices: AddOnService[];
  onAddOnToggle: (addOn: AddOnService) => void;
}

export default function DateTimeSelection({
  booking,
  onBack,
  onSelect,
  addOnServices,
  onAddOnToggle
}: DateTimeSelectionProps) {
  const [selectedDate, setSelectedDate] = useState<Date | null>(null);
  const [selectedTime, setSelectedTime] = useState<string>('');
  const [availableSlots, setAvailableSlots] = useState<string[]>([]);
  const [loading, setLoading] = useState(false);
  const [checkingAvailability, setCheckingAvailability] = useState(false);
  const { showError, showWarning } = useToast();

  const handleDateSelect = (date: Date | null) => {
    setSelectedDate(date);
    setSelectedTime(''); // Reset time when date changes
    if (date) {
      loadAvailableSlots(date);
    }
  };

  const loadAvailableSlots = async (date: Date) => {
    setLoading(true);
    try {
      const dateString = formatDateForAPI(date);

      // Use the admin availability endpoint (public access)
      const response = await fetch(`${API_CONFIG.BASE_URL}/admin/availability/time-slots/${dateString}`);
      if (!response.ok) {
        throw new Error('Failed to fetch availability');
      }

      const data = await response.json();
      if (data.success && data.data.timeSlots) {
        // Filter available time slots from the admin API response
        const availableSlots = data.data.timeSlots
          .filter((slot: any) => {
            // Handle both formats: objects with isAvailable property or just strings
            if (typeof slot === 'string') return true; // String format means it's available
            return slot.isAvailable === true; // Object format, check isAvailable
          })
          .map((slot: any) => {
            // Extract time from both formats
            return typeof slot === 'string' ? slot : slot.time;
          });
        setAvailableSlots(availableSlots);
      } else {
        setAvailableSlots([]);
      }
    } catch (error) {
      console.error('Error loading available slots:', error);
      showError('Unable to load available time slots. Please try again.');
      setAvailableSlots([]);
    } finally {
      setLoading(false);
    }
  };

  const handleTimeSelect = async (time: string) => {
    if (!selectedDate) return;

    setCheckingAvailability(true);
    try {
      const dateString = formatDateForAPI(selectedDate);

      // Use the admin availability check endpoint (public access)
      const response = await fetch(`${API_CONFIG.BASE_URL}/admin/availability/check?date=${dateString}&time=${encodeURIComponent(time)}`);
      if (!response.ok) {
        throw new Error('Failed to check availability');
      }

      const data = await response.json();
      if (data.success) {
        if (!data.data.isAvailable) {
          showWarning(`This time slot is not available${data.data.reason ? `: ${data.data.reason}` : ''}. Please select a different time.`);
          return;
        }

        setSelectedTime(time);
        onSelect(dateString, time);
      } else {
        throw new Error(data.message || 'Failed to check availability');
      }
    } catch (error) {
      console.error('Error checking availability:', error);
      showError('Error checking availability. Please try again.');
    } finally {
      setCheckingAvailability(false);
    }
  };

  // Get minimum date (tomorrow)
  const minDate = new Date();
  minDate.setDate(minDate.getDate() + 1);

  // Get maximum date (3 months from now)
  const maxDate = new Date();
  maxDate.setMonth(maxDate.getMonth() + 3);

  // Filter out Sundays and past dates
  const isDateDisabled = (date: Date) => {
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    // Disable past dates and Sundays (0 = Sunday)
    return date < today || date.getDay() === 0;
  };

  return (
    <div className="app">
      <header className="header">
        <div className="header-content">
          <div className="auth-links">
            <a href="#" className="auth-link">SIGN UP</a>
            <a href="#" className="auth-link">LOG IN</a>
          </div>
        </div>
      </header>

      <main className="main-content">
        <div className="datetime-selection">
          <div className="datetime-header">
            <button className="back-button" onClick={onBack}>
              ← SELECT APPOINTMENT
            </button>
            <h2 className="datetime-title">Date & Time</h2>
          </div>



          {/* Microloc Information Section */}
          <div className="microloc-info-section">
            <div className="microloc-image-container">
              <img
                src="/microloc.jpg"
                alt="Microlocs hairstyle example"
                className="microloc-image"
                onError={(e) => {
                  console.warn('Microloc image not found');
                  e.currentTarget.style.display = 'none';
                }}
              />
            </div>
            <div className="microloc-notice">
              <h4>📖 Please Read Before Proceeding</h4>
              <p>
                Please review the microloc image above and ensure you understand the service details.
                This will help you make an informed decision about your appointment.
              </p>
            </div>
          </div>

          {/* Appointment Summary */}
          <div className="appointment-summary">
            <h3>APPOINTMENT</h3>
            <div className="appointment-card">
              <div className="appointment-details">
                <h4>{booking.selectedService?.name}</h4>
                <p className="appointment-price">${booking.selectedService?.price}</p>
                <p className="appointment-duration">Duration: {booking.selectedService?.duration} hours</p>



                {/* Full Service Description */}
                {booking.selectedService?.description && (
                  <div className="service-description">
                    <h5>Service Details:</h5>
                    <div className="description-content">
                      {booking.selectedService.description.split('\n').map((line: string, index: number) => (
                        <p key={index}>{line}</p>
                      ))}
                    </div>
                  </div>
                )}

                {/* Service Images Gallery - Dynamic Gallery */}
                {(() => {
                  console.log('🔍 DateTimeSelection booking object:', booking);
                  console.log('🔍 DateTimeSelection selectedService:', booking.selectedService);
                  return <ServiceImageGallery service={booking.selectedService} />;
                })()}

                {booking.selectedAddOns.map(addOn => (
                  <div key={addOn.id} className="addon-line">
                    + {addOn.name}, {addOn.duration} minutes @ ${addOn.price}
                  </div>
                ))}

                {/* <p className="appointment-note">*EXISTING CLIENTS ONLY*</p> */}
              </div>
              <button className="close-button">×</button>
            </div>
          </div>

          {/* Add-on Services */}
          <div className="addon-section">
            <h3>ADD TO APPOINTMENT</h3>
            <div className="addon-grid">
              {addOnServices.map(addOn => (
                <div key={addOn.id} className="addon-item">
                  <input
                    type="checkbox"
                    id={addOn.id}
                    checked={booking.selectedAddOns.some(item => item.id === addOn.id)}
                    onChange={() => onAddOnToggle(addOn)}
                  />
                  <label htmlFor={addOn.id}>
                    {addOn.name}
                    <br />
                    + {addOn.duration} minutes @ ${addOn.price}
                  </label>
                </div>
              ))}
            </div>
          </div>

          {/* Calendar */}
          <div className="calendar-section">
            <h3>Select Date</h3>
            <div className="datepicker-wrapper">
              <DatePicker
                selected={selectedDate}
                onChange={handleDateSelect}
                minDate={minDate}
                maxDate={maxDate}
                filterDate={(date) => !isDateDisabled(date)}
                inline
                calendarClassName="custom-calendar"
                dayClassName={(date) => {
                  if (isDateDisabled(date)) return 'disabled-date';
                  return 'available-date';
                }}
              />
            </div>

            {/* Selected Date and Time */}
            {selectedDate && (
              <div className="time-selection">
                <div className="selected-date">
                  <h4>{selectedDate.toLocaleDateString('en-US', {
                    weekday: 'long',
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric'
                  })}</h4>
                  <p className="timezone">
                    TIME ZONE: {Intl.DateTimeFormat().resolvedOptions().timeZone}
                    ({new Date().toLocaleString('en-US', { timeZoneName: 'short' }).split(' ').pop()})
                  </p>
                </div>

                <div className="time-slots">
                  {loading ? (
                    <div className="loading-slots">Loading available times...</div>
                  ) : availableSlots.length > 0 ? (
                    availableSlots.map(time => (
                      <button
                        key={time}
                        className={`time-slot ${selectedTime === time ? 'selected' : ''}`}
                        onClick={() => handleTimeSelect(time)}
                        disabled={checkingAvailability}
                      >
                        {formatTimeSlot(time)}
                      </button>
                    ))
                  ) : (
                    <div className="no-slots">No available time slots for this date</div>
                  )}
                  {checkingAvailability && (
                    <div className="checking-availability">Checking availability...</div>
                  )}
                </div>
              </div>
            )}
          </div>


        </div>
      </main>
    </div>
  );
}

// Service Image Gallery Component
interface ServiceImageGalleryProps {
  service: any;
}

const ServiceImageGallery: React.FC<ServiceImageGalleryProps> = ({ service }) => {
  const [lightboxOpen, setLightboxOpen] = useState(false);
  const [currentImageIndex, setCurrentImageIndex] = useState(0);

  // Create comprehensive list of all available images
  const allImages = [];

  // Add main image if it exists
  if (service?.image) {
    allImages.push({
      src: service.image,
      alt: `${service.name} - Main Style`,
      isMain: true
    });
  }

  // Add images from images array
  if (service?.images && Array.isArray(service.images)) {
    service.images.forEach((imageUrl: string, index: number) => {
      // Don't duplicate the main image
      if (imageUrl !== service?.image) {
        allImages.push({
          src: imageUrl,
          alt: `${service.name} - Style Option ${index + 1}`,
          isMain: false
        });
      }
    });
  }

  console.log('🔍 DateTime Gallery Images:', allImages);

  if (allImages.length === 0) {
    return null;
  }

  const openLightbox = (index: number) => {
    setCurrentImageIndex(index);
    setLightboxOpen(true);
  };

  return (
    <div className="service-gallery-container">
      <h5 className="gallery-title">
        🎨 Available Styles ({allImages.length})
      </h5>

      <div className="gallery-grid">
        {allImages.map((image, index) => (
          <div
            key={index}
            className={`gallery-thumbnail ${image.isMain ? 'main-image' : ''}`}
            onClick={() => openLightbox(index)}
          >
            <img
              src={image.src}
              alt={image.alt}
              className="thumbnail-image"
              onError={(e) => {
                console.error('❌ Failed to load image:', image.src);
                e.currentTarget.style.display = 'none';
              }}
              onLoad={() => {
                console.log('✅ Successfully loaded image:', image.src);
              }}
            />
            {image.isMain && (
              <div className="main-badge">Main</div>
            )}
            <div className="image-overlay">
              <span className="view-text">👁️ View</span>
            </div>
          </div>
        ))}
      </div>

      {lightboxOpen && (
        <Lightbox
          open={lightboxOpen}
          close={() => setLightboxOpen(false)}
          slides={allImages}
          index={currentImageIndex}
        />
      )}
    </div>
  );
};

// Export moved to end of file
