import { useState, useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import Header from '../components/Layout/Header'
import { serviceAPI, type Service } from '../utils/serviceAPI'
import { type User } from '../utils/api'

interface ServicesPageProps {
  currentUser: User | null;
  onLogout: () => void;
}

export default function ServicesPage({ currentUser, onLogout }: ServicesPageProps) {
  const navigate = useNavigate();
  const [services, setServices] = useState<Service[]>([]);
  const [categories, setCategories] = useState<string[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [searchTerm, setSearchTerm] = useState('');
  useEffect(() => {
    fetchServices();
    fetchCategories();
  }, []);

  const fetchServices = async () => {
    try {
      setLoading(true);
      const response = await serviceAPI.getServices({
        isActive: true, // Only show active services to customers
        sortBy: 'category',
        sortOrder: 'asc'
      });

      if (response.success) {
        // Flatten the categorized services into a single array in desired order
        const categoryOrder = [
          'otherservices',    // Services (renamed from "Other Services") - shown first
          'retie',            // Reties section - shown second
          'consultation',
          'installation',
          'locsstyling',
          'retightening4-5weeks',
          'retightening6-7week',
          'retightening8+week'
        ];

        const allServices: Service[] = [];

        // Define service order within each category based on API structure
        const getServiceOrder = (categoryKey: string, serviceName: string): number => {
          if (categoryKey === 'services') {
            // Services category order based on API structure:
            // consultation → installation (Extra Small, Small, Medium, Instant Lock) → locsstyling → otherservices → retie
            const serviceOrder = [
              'CONSULTATION',                    // consultation category
              'EXTRAL SMALL MICROLOCS',         // installation category
              'SMALL SIZE MICROLOCS',           // installation category
              'MEDIUM MICROLOCS',               // installation category
              'INSTANT LOCK',                   // installation category
              'LOCS STYLING',                   // locsstyling category
              'LOCK REPAIRS',                   // otherservices category
              'INSTANT LOCS RETIE'              // retie category
            ];
            return serviceOrder.indexOf(serviceName);
          } else if (categoryKey.includes('retightening')) {
            // Retightening categories order: Extra Small → Medium → Small (based on API data order)
            if (serviceName.includes('Extra Small')) return 0;
            if (serviceName.includes('Medium')) return 1;
            if (serviceName.includes('Small') && !serviceName.includes('Extra Small')) return 2;
          }
          return 999; // Default order for unmatched services
        };

        // Define which API categories should be combined into "Services"
        const serviceCategoryKeys = ['consultation', 'installation', 'locsstyling', 'otherservices', 'retie'];

        categoryOrder.forEach(categoryKey => {
          if (categoryKey === 'services') {
            // Combine all service categories into one "Services" section
            const combinedServices: any[] = [];

            serviceCategoryKeys.forEach((apiCategoryKey: string) => {
              const categoryServices = response.data[apiCategoryKey];
              if (Array.isArray(categoryServices)) {
                combinedServices.push(...categoryServices);
              }
            });

            // Sort all services in the desired order
            const sortedServices = combinedServices.sort((a, b) => {
              const orderA = getServiceOrder('services', a.name);
              const orderB = getServiceOrder('services', b.name);
              return orderA - orderB;
            });

            // Add category property to each service for filtering
            const servicesWithCategory = sortedServices.map(service => ({
              ...service,
              displayCategory: 'Services'
            }));
            allServices.push(...servicesWithCategory);
          } else {
            // Handle retightening categories normally
            const categoryServices = response.data[categoryKey];
            if (Array.isArray(categoryServices)) {
              // Sort services within each category
              const sortedServices = [...categoryServices].sort((a, b) => {
                const orderA = getServiceOrder(categoryKey, a.name);
                const orderB = getServiceOrder(categoryKey, b.name);
                return orderA - orderB;
              });

              // Add category property to each service for filtering
              const servicesWithCategory = sortedServices.map(service => ({
                ...service,
                displayCategory: categoryKey === 'retightening4-5weeks' ? 'Retightening 4-5weeks' :
                                categoryKey === 'retightening6-7week' ? 'Retightening 6-7 week' :
                                categoryKey === 'retightening8+week' ? 'Retightening 8+ week' :
                                categoryKey.charAt(0).toUpperCase() + categoryKey.slice(1)
              }));
              allServices.push(...servicesWithCategory);
            }
          }
        });

        setServices(allServices);
        console.log('🔍 Loaded services with images:', allServices);
        console.log('🔍 First service example:', allServices[0]);
        console.log('🔍 LOCS STYLING service:', allServices.find(s => s.name === 'LOCS STYLING'));
      } else {
        throw new Error('Failed to load services');
      }
    } catch (error) {
      console.error('Error fetching services:', error);
      setError('Failed to load services. Please try again.');
      setServices([]);
    } finally {
      setLoading(false);
    }
  };

  const fetchCategories = async () => {
    try {
      const response = await serviceAPI.getServices();
      if (response.success) {
        // Extract categories from the API response structure in desired order
        const categoryOrder = [
          'services',         // Combined services category (will include consultation, installation, otherservices, locsstyling, retie)
          'retightening4-5weeks',
          'retightening6-7week',
          'retightening8+week'
        ];

        const formattedCategories = categoryOrder
          .filter(key => {
            if (key === 'services') {
              // Check if any of the service categories have data
              const serviceKeys = ['consultation', 'installation', 'locsstyling', 'otherservices', 'retie'];
              return serviceKeys.some((apiKey: string) => response.data[apiKey] && response.data[apiKey].length > 0);
            }
            return response.data[key] && response.data[key].length > 0;
          })
          .map(key => {
            // Convert API keys to readable format
            switch(key) {
              case 'services': return 'Services';  // Combined services category
              case 'retightening4-5weeks': return 'Retightening 4-5weeks';
              case 'retightening6-7week': return 'Retightening 6-7 week';
              case 'retightening8+week': return 'Retightening 8+ week';
              default: return key.charAt(0).toUpperCase() + key.slice(1);
            }
          });
        setCategories(['all', ...formattedCategories]);
      }
    } catch (error) {
      console.error('Error fetching categories:', error);
      // Set default categories if API fails
      setCategories(['all', 'Consultation', 'Installation', 'LOCS STYLING', 'Retie', 'Other Services']);
    }
  };





  // Filter services based on category and search term
  const filteredServices = services.filter(service => {
    const matchesCategory = selectedCategory === 'all' ||
      (service as any).displayCategory === selectedCategory ||
      service.category === selectedCategory;
    const matchesSearch = searchTerm === '' ||
      service.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      service.description.toLowerCase().includes(searchTerm.toLowerCase());

    return matchesCategory && matchesSearch;
  });

  // Group services by display category for display
  const groupedServices = filteredServices.reduce((acc, service) => {
    const categoryKey = (service as any).displayCategory || service.category;
    if (!acc[categoryKey]) {
      acc[categoryKey] = [];
    }
    acc[categoryKey].push(service);
    return acc;
  }, {} as Record<string, Service[]>);

  // Define the display order for categories
  const categoryDisplayOrder = [
    'Services',           // First (combined category with all services)
    'Retightening 4-5weeks',
    'Retightening 6-7 week',
    'Retightening 8+ week'
  ];

  // Sort grouped services by the desired order
  const sortedGroupedServices = Object.keys(groupedServices)
    .sort((a, b) => {
      const indexA = categoryDisplayOrder.indexOf(a);
      const indexB = categoryDisplayOrder.indexOf(b);
      // If category not in order list, put it at the end
      if (indexA === -1) return 1;
      if (indexB === -1) return -1;
      return indexA - indexB;
    })
    .reduce((acc, key) => {
      acc[key] = groupedServices[key];
      return acc;
    }, {} as Record<string, Service[]>);

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(price);
  };

  const formatDuration = (hours: number) => {
    if (hours < 1) {
      return `${Math.round(hours * 60)} min`;
    } else if (hours === 1) {
      return '1 hour';
    } else if (hours % 1 === 0) {
      return `${hours} hours`;
    } else {
      const wholeHours = Math.floor(hours);
      const minutes = Math.round((hours - wholeHours) * 60);
      return `${wholeHours}h ${minutes}m`;
    }
  };

  const handleBookService = async (service: Service) => {
    console.log('🔍 Booking service with data:', service);
    console.log('🔍 Service has image:', service.image);
    console.log('🔍 Service has images:', service.images);

    let serviceWithImages = { ...service };

    // If service doesn't have images, try to fetch complete service data
    if (!service.image && !service.images) {
      try {
        console.log('🔍 Fetching complete service data for:', service.id);
        const completeServiceResponse = await serviceAPI.getService(service.id);
        if (completeServiceResponse.success) {
          serviceWithImages = completeServiceResponse.data;
          console.log('🔍 Fetched complete service data:', serviceWithImages);
        }
      } catch (error) {
        console.error('Failed to fetch complete service data:', error);
      }
    }

    // Fallback: Add known image data for LOCS STYLING service if still missing
    if (service.name === 'LOCS STYLING' && !serviceWithImages.image) {
      serviceWithImages = {
        ...serviceWithImages,
        image: "https://res.cloudinary.com/djeddsyoq/image/upload/v1755717788/microlocs/services/gukxkcdjduaateaks97v.png",
        images: ["https://res.cloudinary.com/djeddsyoq/image/upload/v1755717788/microlocs/services/gukxkcdjduaateaks97v.png"]
      };
      console.log('🔍 Added fallback images to LOCS STYLING service:', serviceWithImages);
    }

    // Store selected service in sessionStorage for booking flow
    sessionStorage.setItem('selectedService', JSON.stringify(serviceWithImages));

    // Navigate to booking with service data in URL params
    const params = new URLSearchParams();
    params.set('service', JSON.stringify(serviceWithImages));
    navigate(`/booking/datetime?${params.toString()}`);
  };

  if (loading) {
    return (
      <div className="app">
        <Header currentUser={currentUser} onLogout={onLogout} />
        <main className="main-content">
          <div className="loading-container">
            <p>Loading services...</p>
          </div>
        </main>
      </div>
    );
  }

  if (error) {
    return (
      <div className="app">
        <Header currentUser={currentUser} onLogout={onLogout} />
        <main className="main-content">
          <div className="error-container">
            <p className="error-message">{error}</p>
            <button
              className="btn-primary"
              onClick={() => {
                setError(null);
                fetchServices();
              }}
            >
              Try Again
            </button>
          </div>
        </main>
      </div>
    );
  }

  return (
    <div className="app">
      <Header currentUser={currentUser} onLogout={onLogout} />
      
      <main className="main-content">
        <div className="page-header">
          <h1>Our Services</h1>
          <p>Choose from our range of professional services</p>
        </div>

        {/* Search and Filter */}
        <div className="services-filters">
          <div className="search-container">
            <input
              type="text"
              placeholder="Search services..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="search-input"
            />
          </div>
          
          <div className="category-filters">
            <button
              className={`filter-btn ${selectedCategory === 'all' ? 'active' : ''}`}
              onClick={() => setSelectedCategory('all')}
            >
              All Services
            </button>
            {categories.map(category => (
              <button
                key={category}
                className={`filter-btn ${selectedCategory === category ? 'active' : ''}`}
                onClick={() => setSelectedCategory(category)}
              >
                {category}
              </button>
            ))}
          </div>
        </div>

        {/* Services Display */}
        {Object.keys(groupedServices).length === 0 ? (
          <div className="empty-state">
            <h3>No services found</h3>
            <p>Try adjusting your search or filter criteria.</p>
          </div>
        ) : (
          <div className="services-content">
            {Object.entries(sortedGroupedServices).map(([category, categoryServices]) => (
              <div key={category} className="service-category-section">
                <h2 className="category-title">{category}</h2>
                <div className="services-grid">
                  {categoryServices.map(service => (
                    <div key={service.id} className="service-card">
                      <div className="service-header">
                        <h3>{service.name}</h3>
                        <div className="service-price">{formatPrice(service.price)}</div>
                      </div>
                      
                      <div className="service-description">
                        {service.description}
                      </div>
                      
                      <div className="service-details">
                        <span className="service-duration">
                          Duration: {formatDuration(service.duration)}
                        </span>
                      </div>



                      <div className="service-actions">
                        <button
                          onClick={() => handleBookService(service)}
                          className="btn-primary"
                        >
                          Book Now
                        </button>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            ))}
          </div>
        )}
      </main>
    </div>
  );
}
