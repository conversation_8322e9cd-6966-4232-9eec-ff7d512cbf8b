"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const models_1 = require("../../models");
const response_1 = require("../../utils/response");
const router = (0, express_1.Router)();
// GET /api/v2/services - Get all services formatted for frontend v2
router.get('/', async (req, res) => {
    try {
        const services = await models_1.Service.find({ isActive: true }).sort({ category: 1, name: 1 });
        // Define custom service order within categories
        const getServiceOrder = (categoryKey, serviceName) => {
            if (categoryKey === 'installation') {
                // Installation order: Extra Small → Small → Medium → Instant Lock
                const installationOrder = [
                    'EXTRAL SMALL MICROLOCS',
                    'SMALL SIZE MICROLOCS',
                    'MEDIUM MICROLOCS',
                    'INSTANT LOCK'
                ];
                const index = installationOrder.indexOf(serviceName);
                return index === -1 ? 999 : index;
            }
            else if (categoryKey.includes('retightening')) {
                // Retightening order: Extra Small → Medium → Small
                if (serviceName.includes('Extra Small'))
                    return 0;
                if (serviceName.includes('Medium'))
                    return 1;
                if (serviceName.includes('Small') && !serviceName.includes('Extra Small'))
                    return 2;
                return 999;
            }
            return 0; // Default order for other categories
        };
        // Group services by category to match frontend v2 structure
        const groupedServices = {};
        services.forEach(service => {
            const categoryKey = service.category.toLowerCase().replace(/\s+/g, '');
            if (!groupedServices[categoryKey]) {
                groupedServices[categoryKey] = [];
            }
            groupedServices[categoryKey].push({
                id: service._id,
                name: service.name,
                price: service.price.toFixed(2),
                description: service.description,
                duration: service.duration,
                category: service.category,
                image: service.image,
                images: service.images
            });
        });
        // Sort services within each category using custom order
        Object.keys(groupedServices).forEach(categoryKey => {
            groupedServices[categoryKey].sort((a, b) => {
                const orderA = getServiceOrder(categoryKey, a.name);
                const orderB = getServiceOrder(categoryKey, b.name);
                return orderA - orderB;
            });
        });
        (0, response_1.sendSuccess)(res, 'Services retrieved successfully', groupedServices);
    }
    catch (error) {
        console.error('Get services error:', error);
        (0, response_1.sendError)(res, error.message);
    }
});
// GET /api/v2/services/categories - Get service categories
router.get('/categories', async (req, res) => {
    try {
        const categories = await models_1.Service.distinct('category', { isActive: true });
        (0, response_1.sendSuccess)(res, 'Service categories retrieved successfully', categories);
    }
    catch (error) {
        console.error('Get service categories error:', error);
        (0, response_1.sendError)(res, error.message);
    }
});
// GET /api/v2/services/:id - Get service by ID
router.get('/:id', async (req, res) => {
    try {
        const { id } = req.params;
        const service = await models_1.Service.findById(id);
        if (!service) {
            (0, response_1.sendError)(res, 'Service not found', undefined, 404);
            return;
        }
        if (!service.isActive) {
            (0, response_1.sendError)(res, 'Service is not available', undefined, 404);
            return;
        }
        const formattedService = {
            id: service._id,
            name: service.name,
            price: service.price.toFixed(2),
            description: service.description,
            duration: service.duration,
            category: service.category,
            image: service.image,
            images: service.images
        };
        (0, response_1.sendSuccess)(res, 'Service retrieved successfully', formattedService);
    }
    catch (error) {
        console.error('Get service error:', error);
        (0, response_1.sendError)(res, error.message);
    }
});
// GET /api/v2/services/category/:category - Get services by category
router.get('/category/:category', async (req, res) => {
    try {
        const { category } = req.params;
        const services = await models_1.Service.find({
            category: new RegExp(category, 'i'),
            isActive: true
        }).sort({ name: 1 });
        const formattedServices = services.map(service => ({
            id: service._id,
            name: service.name,
            price: service.price.toFixed(2),
            description: service.description,
            duration: service.duration,
            category: service.category,
            image: service.image,
            images: service.images
        }));
        (0, response_1.sendSuccess)(res, 'Services retrieved successfully', formattedServices);
    }
    catch (error) {
        console.error('Get services by category error:', error);
        (0, response_1.sendError)(res, error.message);
    }
});
exports.default = router;
