@import url('https://fonts.googleapis.com/css?family=Alegreya:400,600');

/* Reset and base styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Alegreya', Helvetica, sans-serif;
  line-height: 1.5;
  color: #333;
  background-color: #f5f5f5;
}

.app {
  min-height: 100vh;
  background-color: #f5f5f5;
}

/* Header */
.header {
  background-color: white;
  padding: 1rem 0;
  border-bottom: 1px solid #e0e0e0;
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  min-height: 60px;
}

.header-left {
  flex: 1;
}

.logo-link {
  text-decoration: none;
  color: inherit;
}

.logo-container {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.logo-placeholder {
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, #d4af37, #b8860b);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 700;
  font-size: 1.2rem;
  box-shadow: 0 2px 8px rgba(0,0,0,0.2);
}

.business-name-header {
  font-size: 1.5rem;
  font-weight: 600;
  color: #333;
  margin: 0;
}

.auth-links {
  display: flex;
  gap: 0.5rem;
}

.auth-link {
  background: white;
  color: #333;
  text-decoration: none;
  font-weight: 600;
  font-size: 0.8rem;
  letter-spacing: 1px;
  padding: 0.5rem 1rem;
  border: 2px solid #333;
  border-radius: 0;
  transition: all 0.3s ease;
  cursor: pointer;
  text-transform: uppercase;
  min-width: 80px;
  text-align: center;
  display: inline-block;
  user-select: none;
  -webkit-tap-highlight-color: transparent;
  touch-action: manipulation;
}

.auth-link:hover,
.auth-link:focus,
.auth-link:active {
  background: #333;
  color: white;
  outline: none;
}

/* Main Content */
.main-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
}

/* Profile Section */
.profile-section {
  text-align: center;
  margin-bottom: 2rem;
}

.profile-image {
  margin-bottom: 1rem;
}

.profile-avatar {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  border: 3px solid #ddd;
  background: linear-gradient(135deg, #8b6f47, #a67c5a);
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto;
}

.avatar-placeholder {
  color: white;
  font-size: 2rem;
  font-weight: 600;
  text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.business-name {
  font-size: 2rem;
  font-weight: 600;
  color: #333;
  margin: 0;
}

/* Hero Banner */
.hero-banner {
  background: linear-gradient(135deg, #d4af37 0%, #d4af37 30%, #d4af37 70%, #d4af37 100%);
  border-radius: 15px;
  margin: 2rem 0;
  overflow: hidden;
  position: relative;
  box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.hero-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 3rem;
  position: relative;
}

.hero-text {
  flex: 1;
  color: white;
  text-align: center;
}

.hero-title {
  font-size: 4rem;
  font-weight: 600;
  margin: 0;
  text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.hero-subtitle {
  font-size: 2rem;
  font-style: italic;
  margin: 0;
  text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
}

.location {
  font-size: 1.2rem;
  font-weight: 600;
  letter-spacing: 2px;
  margin: 1rem 0;
  text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
}

.welcome-text {
  font-size: 1.1rem;
  margin: 1rem 0 0 0;
  text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
}

.hero-image {
  flex: 0 0 300px;
  text-align: center;
}

.stylist-image {
  width: 250px;
  height: 350px;
  border-radius: 10px;
  box-shadow: 0 8px 20px rgba(0,0,0,0.3);
  background: linear-gradient(135deg, #d4af37, #d4af37);
  display: flex;
  align-items: center;
  justify-content: center;
}

.stylist-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 10px;
  background: linear-gradient(135deg, rgba(255,255,255,0.1), rgba(255,255,255,0.05));
}

.stylist-silhouette {
  width: 120px;
  height: 180px;
  background: rgba(0,0,0,0.3);
  border-radius: 60px 60px 20px 20px;
  position: relative;
}

.stylist-silhouette::before {
  content: '';
  position: absolute;
  top: -30px;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 60px;
  background: rgba(0,0,0,0.3);
  border-radius: 50%;
}

/* Info Sections */
.info-sections {
  margin: 3rem 0;
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.info-card {
  background: linear-gradient(135deg, #d4af37 0%, #d4af37 50%, #d4af37 100%);
  border-radius: 15px;
  padding: 3rem;
  color: white;
  position: relative;
  overflow: hidden;
  box-shadow: 0 8px 25px rgba(0,0,0,0.2);
  margin-bottom: 1rem;
}

/* Decorative elements for info cards */
.info-card::before,
.info-card::after {
  content: '';
  position: absolute;
  width: 80px;
  height: 120px;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 80 120'%3E%3Cpath d='M10 10 C15 5 25 5 30 10 L35 20 C40 25 40 35 35 40 L30 50 C25 55 25 65 30 70 L35 80 C40 85 40 95 35 100 L30 110 C25 115 15 115 10 110 L5 100 C0 95 0 85 5 80 L10 70 C15 65 15 55 10 50 L5 40 C0 35 0 25 5 20 Z' fill='%23000' opacity='0.15'/%3E%3Cpath d='M15 15 C18 12 22 12 25 15 L28 22 C30 25 30 30 28 33 L25 40 C22 43 22 48 25 51 L28 58 C30 61 30 66 28 69 L25 76 C22 79 18 79 15 76 L12 69 C10 66 10 61 12 58 L15 51 C18 48 18 43 15 40 L12 33 C10 30 10 25 12 22 Z' fill='%23000' opacity='0.1'/%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-size: contain;
}

.info-card::before {
  left: 1rem;
  top: 50%;
  transform: translateY(-50%);
}

.info-card::after {
  right: 1rem;
  top: 50%;
  transform: translateY(-50%) scaleX(-1);
}

.info-title {
  font-size: 2.5rem;
  font-weight: 600;
  text-align: center;
  margin-bottom: 2rem;
  text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
  position: relative;
}

.info-title::after {
  content: '';
  position: absolute;
  bottom: -15px;
  left: 50%;
  transform: translateX(-50%);
  width: 300px;
  height: 4px;
  background: linear-gradient(90deg, transparent 0%, #ffd700 20%, #ffed4e 50%, #ffd700 80%, transparent 100%);
  border-radius: 2px;
}

.info-content {
  text-align: center;
  font-size: 1.1rem;
  line-height: 1.6;
  position: relative;
  z-index: 1;
}

.info-content p {
  margin-bottom: 1.5rem;
}

.methods-title,
.package-title {
  font-weight: 600;
  font-size: 1.2rem;
  margin: 2rem 0 1rem 0;
}

.methods-list,
.package-list {
  list-style: none;
  margin: 1rem 0;
}

.methods-list li,
.package-list li {
  margin: 0.5rem 0;
  font-weight: 600;
  letter-spacing: 1px;
}

.price-item {
  margin: 1.5rem 0;
}

.price-item h4 {
  font-size: 1.4rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.price-note {
  font-size: 0.9rem;
  font-style: italic;
  margin-top: 2rem;
  padding: 1rem;
  background: rgba(0,0,0,0.2);
  border-radius: 8px;
}

/* Reties Pricing Styles */
.reties-title {
  font-size: 2rem;
  font-weight: 600;
  text-align: center;
  margin: 2rem 0 1.5rem 0;
}

.reties-pricing {
  margin: 2rem 0;
}

.retie-period {
  margin-bottom: 2.5rem;
  padding: 1.5rem;
  background: #f8f9fa;
  border-radius: 12px;
  border-left: 4px solid #d4af37;
}

.retie-period:last-child {
  margin-bottom: 0;
}

.retie-period-title {
  font-size: 1.4rem;
  font-weight: 700;
  color: #2c5530;
  margin: 0 0 1.5rem 0;
  text-align: center;
  padding-bottom: 0.8rem;
  border-bottom: 2px solid #d4af37;
}

.retie-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin: 1rem 0;
  font-size: 1.1rem;
  font-weight: 500;
  padding: 0.8rem;
  color: #333;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.retie-item.clickable {
  cursor: pointer;
  background: white;
  border: 2px solid transparent;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.retie-item.clickable:hover {
  background: #f8f9fa;
  border-color: #d4af37;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

.retie-duration {
  flex: 0 0 auto;
  color: #2c5530;
  font-weight: 600;
}

.retie-dots {
  flex: 1;
  text-align: center;
  letter-spacing: 2px;
  margin: 0 1rem;
  color: #d4af37;
  font-weight: 600;
}

.retie-price {
  flex: 0 0 auto;
  color: #2c5530;
  font-weight: 700;
  font-size: 1.2rem;
}

.retie-note {
  font-size: 1rem;
  margin: 1.5rem 0;
  text-align: center;
}

.retie-book-btn {
  background: #2c5530;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  font-weight: 600;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.3s ease;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-left: 1rem;
}

.retie-book-btn:hover {
  background: #1e3a22;
  transform: scale(1.05);
}

.retie-book-btn:active {
  transform: scale(0.95);
}

/* Other Services Styles */
.other-services {
  margin-top: -30px;
  padding-top: -50px;
  border-top: 2px solid #979694;
}

.other-services-title {
  font-size: 1.8rem;
  font-weight: 600;
  text-align: center;
  margin-bottom: 2rem;
  color: #2c5530;
}

.other-services .service-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin: 1rem 0;
  padding: 1rem;
  background: white;
  border-radius: 8px;
  border: 2px solid transparent;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  cursor: pointer;
  transition: all 0.3s ease;
}

.other-services .service-item:hover {
  background: #f8f9fa;
  border-color: #d4af37;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

.other-services .service-info {
  display: flex;
  align-items: center;
  flex: 1;
}

.other-services .service-name {
  flex: 0 0 auto;
  color: #2c5530;
  font-weight: 600;
  font-size: 1.1rem;
}

.other-services .service-dots {
  flex: 1;
  text-align: center;
  letter-spacing: 2px;
  margin: 0 1rem;
  color: #d4af37;
  font-weight: 600;
}

.other-services .service-price {
  flex: 0 0 auto;
  color: #2c5530;
  font-weight: 700;
  font-size: 1.2rem;
}

/* Policies Section */
.policies-section {
  padding: 4rem 2rem;
  background: linear-gradient(135deg, #d4af37 0%, #d4af37 100%);
}

.policies-card {
  max-width: 1200px;
  margin: 0 auto;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20px;
  padding: 3rem;
  box-shadow: 0 20px 40px rgba(0,0,0,0.1);
}

.policies-title {
  font-size: 2.5rem;
  font-weight: 600;
  text-align: center;
  color: #2c5530;
  margin-bottom: 3rem;
  font-family: 'Playfair Display', serif;
}

.policies-content {
  margin-top: 2rem;
}

.policies-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.policies-list li {
  font-size: 1rem;
  line-height: 1.6;
  color: #5d4e37;
  position: relative;
  padding-left: 2rem;
  margin-bottom: 1rem;
}

.policies-list li:before {
  content: "•";
  color: #d4af37;
  font-weight: bold;
  font-size: 1.5rem;
  position: absolute;
  left: 0;
  top: 0;
}

.policies-list li strong {
  color: #2c5530;
  font-weight: 600;
}

.payment-methods-single {
  margin-top: 1rem;
}

.payment-method-combined {
  background: #f8f8f8;
  border-radius: 8px;
  padding: 1.5rem;
  border-left: 4px solid #d4af37;
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.payment-option {
  width: 100%;
}

.policies-footer {
  margin-top: 2rem;
  padding-top: 2rem;
  border-top: 2px solid #e0e0e0;
  text-align: center;
}

.policies-footer p {
  font-size: 1.1rem;
  color: #2c5530;
  font-style: italic;
  margin: 0;
}

/* Disclaimer Section */
.disclaimer-section {
  padding: 3rem 2rem;
  background: linear-gradient(135deg, #d4af37 0%, #d4af37 100%);
}

.disclaimer-card {
  max-width: 800px;
  margin: 0 auto;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20px;
  padding: 2.5rem;
  box-shadow: 0 20px 40px rgba(0,0,0,0.1);
}

.disclaimer-title {
  font-size: 2rem;
  font-weight: 600;
  text-align: center;
  color: #2c5530;
  margin-bottom: 2rem;
  font-family: 'Playfair Display', serif;
}

.disclaimer-content {
  font-size: 1rem;
  line-height: 1.7;
  color: #5d4e37;
  text-align: left;
}

.disclaimer-content p {
  margin-bottom: 1.5rem;
}

/* Consultation Section */
.consultation-section {
  padding: 4rem 2rem;
  background: linear-gradient(135deg, #d4af37 0%, #d4af37 100%);
}

.consultation-card {
  max-width: 800px;
  margin: 0 auto;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20px;
  padding: 3rem;
  box-shadow: 0 20px 40px rgba(0,0,0,0.1);
  text-align: center;
}

.consultation-title {
  font-size: 2rem;
  font-weight: 700;
  color: #2c5530;
  margin-bottom: 1rem;
  font-family: 'Playfair Display', serif;
}

.consultation-subtitle {
  font-size: 1.8rem;
  font-weight: 600;
  color: #2c5530;
  margin-bottom: 2rem;
  line-height: 1.2;
}

.consultation-intro {
  font-size: 1.1rem;
  color: #5d4e37;
  margin-bottom: 1.5rem;
  line-height: 1.6;
}

.consultation-options-title {
  font-size: 1.2rem;
  font-weight: 600;
  color: #2c5530;
  margin-bottom: 2rem;
}

.consultation-options {
  display: flex;
  justify-content: center;
  gap: 2rem;
  margin-bottom: 2rem;
  flex-wrap: wrap;
}

.consultation-option h4 {
  font-size: 1.1rem;
  font-weight: 600;
  color: #2c5530;
  margin: 0;
  padding: 1rem 2rem;
  background: #e8f5e8;
  border-radius: 10px;
  border-left: 4px solid #2c5530;
}

.consultation-duration {
  font-size: 1rem;
  color: #5d4e37;
  margin-bottom: 2rem;
  font-weight: 500;
}

.consultation-benefits {
  text-align: left;
  max-width: 500px;
  margin: 0 auto 2rem auto;
}

.benefit-item {
  font-size: 1rem;
  color: #2c5530;
  margin-bottom: 0.8rem;
  padding-left: 1rem;
  position: relative;
}

.consultation-note {
  font-size: 1rem;
  color: #5d4e37;
  margin-bottom: 2rem;
  font-style: italic;
  line-height: 1.6;
}

.consultation-cta {
  background: linear-gradient(135deg, #2c5530 0%, #4a7c59 100%);
  color: white;
  border: none;
  padding: 1rem 2rem;
  font-size: 1.1rem;
  font-weight: 600;
  border-radius: 50px;
  cursor: pointer;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.consultation-cta:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(44, 85, 48, 0.3);
}

/* Email Input with Status */
.email-input-wrapper {
  position: relative;
}

.email-checking,
.email-exists,
.email-new {
  position: absolute;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 0.8rem;
  font-weight: 500;
}

.email-checking {
  color: #666;
}

.email-exists {
  color: #28a745;
}

.email-new {
  color: #007bff;
}

/* Payment Source Selection */
.payment-source-selection {
  margin: 1.5rem 0;
}

.payment-source-selection h5 {
  font-size: 1rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: #333;
}

.payment-source-options {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.payment-source-option {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.8rem 1.2rem;
  border: 2px solid #e0e0e0;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  background: white;
}

.payment-source-option:hover {
  border-color: #007bff;
  background: #f8f9fa;
}

.payment-source-option input[type="radio"] {
  margin: 0;
}

.payment-source-option input[type="radio"]:checked + span {
  color: #007bff;
  font-weight: 600;
}

.payment-source-option:has(input[type="radio"]:checked) {
  border-color: #007bff;
  background: #e7f3ff;
}

/* Compact Calendar Styles */
.calendar-section {
  margin-top: 2rem;
}

.calendar-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  padding: 0 1rem;
}

.calendar-header h3 {
  margin: 0;
  font-size: 1.2rem;
  font-weight: 600;
}

.nav-button {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 50%;
  transition: background-color 0.3s ease;
}

.nav-button:hover {
  background-color: #f0f0f0;
}

.calendar-grid {
  background: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.calendar-days {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  background: #f8f9fa;
}

.day-header {
  padding: 0.8rem 0.5rem;
  text-align: center;
  font-weight: 600;
  font-size: 0.9rem;
  color: #666;
  border-right: 1px solid #e0e0e0;
}

.day-header:last-child {
  border-right: none;
}

.calendar-dates {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
}

.date-cell {
  padding: 1rem 0.5rem;
  text-align: center;
  border-right: 1px solid #e0e0e0;
  border-bottom: 1px solid #e0e0e0;
  cursor: pointer;
  transition: all 0.3s ease;
  min-height: 3rem;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.9rem;
}

.date-cell:last-child {
  border-right: none;
}

.date-cell.prev-month {
  color: #ccc;
  cursor: not-allowed;
}

.date-cell.available {
  background: #e7f3ff;
  color: #007bff;
  font-weight: 600;
}

.date-cell.available:hover {
  background: #d1ecf1;
}

.date-cell.selected {
  background: #007bff;
  color: white;
  font-weight: 600;
}

.time-selection {
  margin-top: 1.5rem;
  padding: 1.5rem;
  background: #f8f9fa;
  border-radius: 8px;
}

.selected-date h4 {
  margin: 0 0 0.5rem 0;
  font-size: 1.1rem;
  color: #333;
}

.timezone {
  font-size: 0.8rem;
  color: #666;
  margin: 0 0 1rem 0;
}

.time-slots {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.time-slot {
  padding: 0.8rem 1.5rem;
  background: white;
  border: 2px solid #e0e0e0;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 500;
}

.time-slot:hover {
  border-color: #007bff;
  background: #e7f3ff;
}

.consultation-actions {
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
  margin-bottom: 2rem;
}

.read-more-btn {
  background: transparent;
  color: #2c5530;
  border: 2px solid #2c5530;
  padding: 0.8rem 1.5rem;
  font-size: 1rem;
  font-weight: 600;
  border-radius: 50px;
  cursor: pointer;
  transition: all 0.3s ease;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.read-more-btn:hover {
  background: #2c5530;
  color: white;
  transform: translateY(-2px);
}

.consultation-details {
  margin-top: 2rem;
  padding-top: 2rem;
  border-top: 2px solid #e0e0e0;
  text-align: left;
}

.consultation-prep,
.installation-prep,
.transfer-consultation {
  margin-bottom: 2rem;
}

.consultation-details h4 {
  font-size: 1.3rem;
  font-weight: 600;
  color: #2c5530;
  margin-bottom: 1rem;
  text-align: center;
}

.consultation-details ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.consultation-details li {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 1rem;
  margin-bottom: 0.8rem;
  border-left: 4px solid #2c5530;
  font-size: 0.95rem;
  line-height: 1.5;
  color: #5d4e37;
}

.consultation-details li strong {
  color: #2c5530;
}

.transfer-consultation p {
  font-size: 1rem;
  line-height: 1.6;
  color: #5d4e37;
  margin-bottom: 1rem;
  text-align: center;
}

/* Important Notice Section */
.important-notice-section {
  padding: 3rem 2rem;
  background: linear-gradient(135deg, #d4af37 0%, #d4af37 100%);
}

.important-notice-card {
  max-width: 800px;
  margin: 0 auto;
  background: linear-gradient(135deg, #d4af37 0%, #b8860b 100%);
  border-radius: 20px;
  padding: 3rem;
  box-shadow: 0 20px 40px rgba(0,0,0,0.2);
}

.notice-title {
  font-size: 2.2rem;
  font-weight: 700;
  text-align: center;
  color: #2c5530;
  margin-bottom: 2rem;
  font-family: 'Playfair Display', serif;
}

.notice-content {
  font-size: 1.1rem;
  line-height: 1.8;
  color: #5d4e37;
}

.notice-content p {
  margin-bottom: 1.5rem;
}

.notice-content strong {
  font-weight: 700;
  color: #2c5530;
}

/* Social Media Section */
.social-section {
  padding: 2rem 2rem;
  background: linear-gradient(135deg, #d4af37 0%, #d4af37 100%);
}

.social-card {
  max-width: 1000px;
  margin: 0 auto;
  background: linear-gradient(135deg, #d4af37 0%, #b8860b 100%);
  border-radius: 20px;
  padding: 3rem;
  box-shadow: 0 20px 40px rgba(0,0,0,0.2);
}

.social-content {
  display: flex;
  align-items: center;
  gap: 3rem;
}

.social-text {
  flex: 1;
}

.social-title {
  font-size: 2.5rem;
  font-weight: 300;
  color: #2c5530;
  margin-bottom: 1rem;
  font-family: 'Playfair Display', serif;
}

.social-script {
  font-size: 2.8rem;
  font-family: 'Dancing Script', cursive;
  color: #5d4e37;
  margin: 1rem 0;
  font-weight: 600;
}

.social-hashtag {
  font-size: 1.5rem;
  font-weight: 600;
  color: #2c5530;
  margin: 0.5rem 0;
}

.social-handle {
  font-size: 1.3rem;
  color: #5d4e37;
  margin-bottom: 2rem;
}

.social-cta {
  font-size: 1.1rem;
  font-weight: 600;
  color: #2c5530;
  margin-bottom: 1rem;
}

.social-arrow {
  font-size: 2rem;
  color: #2c5530;
  text-align: center;
}

.social-phone {
  flex: 0 0 200px;
}

.phone-mockup {
  width: 180px;
  height: 320px;
  background: #2c2c2c;
  border-radius: 25px;
  padding: 15px;
  box-shadow: 0 15px 30px rgba(0,0,0,0.3);
}

.phone-screen {
  width: 100%;
  height: 100%;
  background: #000;
  border-radius: 15px;
  overflow: hidden;
  position: relative;
}

.hair-image-placeholder {
  width: 100%;
  height: 100%;
  background: linear-gradient(45deg, #d4af37, #d4af37, #d4af37);
  position: relative;
}

.hair-texture {
  width: 100%;
  height: 100%;
  background-image:
    radial-gradient(circle at 20% 30%, rgba(139, 69, 19, 0.8) 2px, transparent 2px),
    radial-gradient(circle at 60% 70%, rgba(160, 82, 45, 0.6) 1px, transparent 1px),
    radial-gradient(circle at 80% 20%, rgba(210, 105, 30, 0.7) 1.5px, transparent 1.5px);
  background-size: 15px 15px, 10px 10px, 20px 20px;
}

/* Booking Section */
.booking-section {
  background: white;
  border-radius: 15px;
  padding: 2rem;
  box-shadow: 0 4px 20px rgba(0,0,0,0.1);
  margin: 3rem 0;
}

.booking-header {
  margin-bottom: 2rem;
}

.booking-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #333;
  margin: 0;
  padding-bottom: 1rem;
  border-bottom: 2px solid #f0f0f0;
}

.service-category {
  margin-bottom: 3rem;
}

.category-title {
  font-size: 1.3rem;
  font-weight: 600;
  color: #333;
  margin-bottom: 1.5rem;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid #e0e0e0;
}

.service-item {
  display: flex;
  justify-content: space-between;
  align-items: stretch;
  padding: 1.5rem;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  margin-bottom: 1rem;
  background: #fafafa;
  min-height: 120px;
}

.service-info {
  flex: 1;
  margin-right: 2rem;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.service-header {
  margin-bottom: 1rem;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.service-name {
  font-size: 1.1rem;
  font-weight: 600;
  color: #333;
  margin: 0 0 0.5rem 0;
  line-height: 1.3;
}

.service-price {
  font-size: 1.2rem;
  font-weight: 600;
  color: #8b6f47;
  margin: 0;
}

.service-description {
  font-size: 0.9rem;
  color: #666;
  line-height: 1.4;
  margin: 0;
  white-space: pre-line;
  flex-grow: 1;
}

.book-button {
  background: #000;
  color: white;
  border: none;
  padding: 0.8rem 2rem;
  border-radius: 4px;
  font-weight: 600;
  font-size: 0.9rem;
  letter-spacing: 1px;
  cursor: pointer;
  transition: background-color 0.3s ease;
  flex-shrink: 0;
  align-self: center;
  height: fit-content;
}

.book-button:hover {
  background: #333;
}

.show-all-button {
  background: transparent;
  color: #666;
  border: 1px solid #ccc;
  padding: 0.8rem 2rem;
  border-radius: 4px;
  font-weight: 600;
  font-size: 0.9rem;
  letter-spacing: 1px;
  cursor: pointer;
  margin: 1rem 0;
  transition: all 0.3s ease;
}

.show-all-button:hover {
  background: #f0f0f0;
  border-color: #999;
}

/* Responsive Design */
@media (max-width: 768px) {
  .main-content {
    padding: 1rem;
  }

  .hero-content {
    flex-direction: column;
    text-align: center;
    padding: 2rem;
  }

  .hero-image {
    flex: none;
    margin-top: 2rem;
  }

  .hero-title {
    font-size: 3rem;
  }

  .hero-subtitle {
    font-size: 1.5rem;
  }

  .info-card {
    padding: 2rem;
  }

  .info-card::before,
  .info-card::after {
    display: none;
  }

  .info-title {
    font-size: 2rem;
  }

  .service-item {
    flex-direction: row;
    align-items: stretch;
    min-height: auto;
    padding: 1rem;
  }

  .service-info {
    margin-right: 1rem;
    margin-bottom: 0;
  }

  .service-header {
    margin-bottom: 0.5rem;
  }

  .book-button {
    align-self: center;
    width: auto;
    text-align: center;
    padding: 0.6rem 1.2rem;
    font-size: 0.8rem;
  }

  .auth-links {
    gap: 0.25rem;
  }

  .auth-link {
    font-size: 0.7rem;
    padding: 0.4rem 0.8rem;
    min-width: 70px;
  }

  /* Policies responsive */
  .policies-card {
    padding: 2rem;
  }

  .policies-title {
    font-size: 2rem;
  }

  /* Retie periods responsive */
  .retie-period {
    padding: 1rem;
    margin-bottom: 1.5rem;
  }

  .retie-period-title {
    font-size: 1.2rem;
    margin-bottom: 1rem;
  }

  .retie-item {
    font-size: 1rem;
    margin: 0.8rem 0;
  }

  .policies-list li {
    padding: 1.2rem;
    padding-left: 2rem;
  }

  .payment-method-combined {
    gap: 1rem;
  }

  /* Consultation responsive */
  .consultation-card {
    padding: 2rem;
  }

  .consultation-title {
    font-size: 1.8rem;
  }

  .consultation-subtitle {
    font-size: 1.5rem;
  }

  .consultation-options {
    flex-direction: column;
    gap: 1rem;
  }

  /* Disclaimer responsive */
  .disclaimer-card {
    padding: 2rem;
  }

  .disclaimer-title {
    font-size: 1.8rem;
  }

  /* Important Notice responsive */
  .important-notice-card {
    padding: 2rem;
  }

  .notice-title {
    font-size: 1.8rem;
  }

  .notice-content {
    font-size: 1rem;
  }

  /* Social Media responsive */
  .social-content {
    flex-direction: column;
    text-align: center;
    gap: 2rem;
  }

  .social-title {
    font-size: 2rem;
  }

  .social-script {
    font-size: 2.2rem;
  }

  .phone-mockup {
    width: 150px;
    height: 270px;
  }
}

@media (max-width: 480px) {
  .hero-title {
    font-size: 2.5rem;
  }

  .hero-subtitle {
    font-size: 1.2rem;
  }

  .location {
    font-size: 1rem;
  }

  .info-title {
    font-size: 1.8rem;
  }

  .info-content {
    font-size: 1rem;
  }

  .booking-section {
    padding: 1rem;
  }

  .service-item {
    padding: 1rem;
  }

  /* Policies responsive for small screens */
  .policies-card {
    padding: 1.5rem;
  }

  .policies-title {
    font-size: 1.8rem;
  }

  .policies-list li {
    padding: 1rem;
    padding-left: 1.8rem;
    font-size: 0.9rem;
  }

  .policies-list li:before {
    left: 0.8rem;
    top: 1rem;
    font-size: 1.2rem;
  }

  /* Consultation responsive for small screens */
  .consultation-card {
    padding: 1.5rem;
  }

  .consultation-title {
    font-size: 1.5rem;
  }

  .consultation-subtitle {
    font-size: 1.3rem;
  }

  .consultation-cta {
    padding: 0.8rem 1.5rem;
    font-size: 1rem;
  }

  .consultation-actions {
    flex-direction: column;
    gap: 0.8rem;
  }

  .read-more-btn {
    padding: 0.6rem 1.2rem;
    font-size: 0.9rem;
  }

  /* Disclaimer responsive for small screens */
  .disclaimer-card {
    padding: 1.5rem;
  }

  .disclaimer-title {
    font-size: 1.5rem;
  }

  /* Email input responsive */
  .email-checking,
  .email-exists,
  .email-new {
    position: static;
    display: block;
    margin-top: 0.5rem;
    text-align: center;
  }

  /* Payment source responsive */
  .payment-source-options {
    flex-direction: column;
  }

  .payment-source-option {
    justify-content: center;
  }

  /* Calendar responsive */
  .calendar-header {
    padding: 0 0.5rem;
  }

  .calendar-header h3 {
    font-size: 1.1rem;
  }

  .date-cell {
    padding: 0.8rem 0.3rem;
    min-height: 2.5rem;
    font-size: 0.8rem;
  }

  .time-slots {
    justify-content: center;
  }

  .time-slot {
    padding: 0.6rem 1rem;
    font-size: 0.9rem;
  }

  /* Important Notice responsive for small screens */
  .important-notice-card {
    padding: 1.5rem;
  }

  .notice-title {
    font-size: 1.5rem;
  }

  /* Social Media responsive for small screens */
  .social-card {
    padding: 2rem;
  }

  .social-title {
    font-size: 1.8rem;
  }

  .social-script {
    font-size: 2rem;
  }

  .phone-mockup {
    width: 120px;
    height: 220px;
  }
}

/* DateTime Selection Styles */
.datetime-selection {
  max-width: 1000px;
  margin: 0 auto;
  padding: 2rem;
}

.datetime-header {
  display: flex;
  align-items: center;
  margin-bottom: 2rem;
}

.back-button {
  background: none;
  border: none;
  font-size: 1rem;
  color: #666;
  cursor: pointer;
  margin-right: 2rem;
}

.datetime-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #333;
  margin: 0;
}

.appointment-summary {
  margin-bottom: 2rem;
}

.appointment-summary h3 {
  font-size: 1rem;
  font-weight: 600;
  color: #333;
  margin-bottom: 1rem;
}

.appointment-card {
  background: #f8f8f8;
  border-radius: 8px;
  padding: 1.5rem;
  position: relative;
}

.appointment-details h4 {
  font-size: 1.1rem;
  font-weight: 600;
  color: #333;
  margin: 0 0 0.5rem 0;
}

.appointment-price {
  font-size: 1rem;
  font-weight: 600;
  color: #666;
  margin: 0 0 0.5rem 0;
}

.appointment-datetime {
  font-size: 0.9rem;
  color: #666;
  margin: 0 0 1rem 0;
}

.addon-line {
  font-size: 0.9rem;
  color: #666;
  margin: 0.25rem 0;
}

.appointment-note {
  font-size: 0.9rem;
  color: #666;
  margin: 1rem 0 0 0;
  font-style: italic;
}

.close-button {
  position: absolute;
  top: 1rem;
  right: 1rem;
  background: none;
  border: none;
  font-size: 1.5rem;
  color: #999;
  cursor: pointer;
}

.addon-section {
  margin-bottom: 2rem;
}

.addon-section h3 {
  font-size: 1rem;
  font-weight: 600;
  color: #333;
  margin-bottom: 1rem;
}

.addon-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1rem;
}

.addon-item {
  display: flex;
  align-items: flex-start;
  gap: 0.5rem;
}

.addon-item input[type="checkbox"] {
  margin-top: 0.25rem;
}

.addon-item label {
  font-size: 0.9rem;
  color: #333;
  line-height: 1.4;
  cursor: pointer;
}

.calendar-section {
  background: white;
  border-radius: 8px;
  padding: 1.5rem;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.calendar-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 1rem;
}

.calendar-header h3 {
  font-size: 1.2rem;
  font-weight: 600;
  color: #333;
  margin: 0;
}

.nav-button {
  background: none;
  border: none;
  font-size: 1.2rem;
  color: #666;
  cursor: pointer;
  padding: 0.5rem;
}

.calendar-grid {
  margin-bottom: 2rem;
}

.calendar-days {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 0.5rem;
  margin-bottom: 0.5rem;
}

.day-header {
  text-align: center;
  font-size: 0.9rem;
  font-weight: 600;
  color: #666;
  padding: 0.5rem;
}

.calendar-dates {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 0.5rem;
}

.date-cell {
  text-align: center;
  padding: 0.75rem;
  border-radius: 50%;
  cursor: pointer;
  font-size: 0.9rem;
  transition: all 0.2s ease;
}

.date-cell:hover {
  background: #f0f0f0;
}

.date-cell.prev-month {
  color: #ccc;
}

.date-cell.selected {
  background: #000;
  color: white;
}

.date-cell.available {
  background: #000;
  color: white;
}

.time-selection {
  border-top: 1px solid #e0e0e0;
  padding-top: 1.5rem;
}

.selected-date h4 {
  font-size: 1.1rem;
  font-weight: 600;
  color: #333;
  margin: 0 0 0.5rem 0;
}

.timezone {
  font-size: 0.8rem;
  color: #666;
  margin: 0 0 1rem 0;
}

.time-slots {
  display: flex;
  gap: 1rem;
}

.time-slot {
  background: white;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 0.75rem 1.5rem;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.time-slot:hover {
  border-color: #999;
  background: #f8f8f8;
}

/* Booking Details Styles */
.booking-details {
  max-width: 800px;
  margin: 0 auto;
  padding: 2rem;
}

.customer-info {
  background: white;
  border-radius: 8px;
  padding: 2rem;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
  margin-top: 2rem;
}

.customer-info h3 {
  font-size: 1rem;
  font-weight: 600;
  color: #333;
  margin: 0 0 2rem 0;
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-group label {
  display: block;
  font-size: 0.9rem;
  font-weight: 600;
  color: #333;
  margin-bottom: 0.5rem;
}

.form-group input {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 1rem;
  transition: border-color 0.2s ease;
}

.form-group input:focus {
  outline: none;
  border-color: #666;
}

.phone-input {
  display: flex;
  align-items: center;
  border: 1px solid #ddd;
  border-radius: 4px;
  overflow: hidden;
}

.country-code {
  background: #f8f8f8;
  padding: 0.75rem;
  font-size: 0.9rem;
  color: #666;
  border-right: 1px solid #ddd;
}

.phone-input input {
  border: none;
  flex: 1;
}

.country-select {
  background: #f8f9fa;
  border: none;
  padding: 8px 12px;
  font-size: 0.9rem;
  color: #666;
  border-right: 1px solid #ddd;
  cursor: pointer;
  outline: none;
}

.form-group small {
  display: block;
  font-size: 0.8rem;
  color: #666;
  margin-top: 0.5rem;
}

.continue-button {
  background: #000;
  color: white;
  border: none;
  padding: 1rem 2rem;
  border-radius: 4px;
  font-size: 0.9rem;
  font-weight: 600;
  letter-spacing: 1px;
  cursor: pointer;
  transition: background-color 0.3s ease;
  width: 100%;
  margin-top: 1rem;
}

.continue-button:hover {
  background: #333;
}

/* Responsive for DateTime and Booking Details */
@media (max-width: 768px) {
  .datetime-selection,
  .booking-details {
    padding: 1rem;
  }

  .datetime-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }

  .addon-grid {
    grid-template-columns: 1fr;
  }

  .time-slots {
    flex-direction: column;
  }

  .customer-info {
    padding: 1rem;
    margin-top: 1rem;
  }

  .customer-info h3 {
    font-size: 1.1rem;
    margin-bottom: 1.5rem;
    text-align: center;
  }

  .form-group {
    margin-bottom: 1.2rem;
  }

  .form-group label {
    font-size: 0.85rem;
    margin-bottom: 0.4rem;
  }

  .form-group input {
    padding: 0.9rem;
    font-size: 1rem;
    border-radius: 6px;
  }

  .continue-button {
    padding: 1rem;
    font-size: 1rem;
    margin-top: 1.5rem;
  }

  .appointment-summary {
    margin-bottom: 1rem;
  }

  .appointment-card {
    padding: 1rem;
  }

  .appointment-details h4 {
    font-size: 1rem;
  }

  .appointment-price {
    font-size: 1.1rem;
  }

  .appointment-datetime {
    font-size: 0.9rem;
  }
}

/* Extra small mobile devices - Better form layout */
@media (max-width: 480px) {
  .booking-details {
    padding: 0.5rem;
  }

  .customer-info {
    padding: 1rem;
    border-radius: 6px;
    margin-top: 1rem;
  }

  .customer-info h3 {
    font-size: 1rem;
    margin-bottom: 1rem;
    text-align: left;
    letter-spacing: 0.5px;
  }

  .form-group {
    margin-bottom: 1rem;
  }

  .form-group label {
    font-size: 0.8rem;
    font-weight: 700;
    color: #333;
    margin-bottom: 0.3rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
  }

  .form-group input {
    width: 100%;
    padding: 0.8rem;
    font-size: 1rem;
    border: 1px solid #ddd;
    border-radius: 4px;
    background: white;
    box-sizing: border-box;
  }

  .form-group input:focus {
    outline: none;
    border-color: #000;
    box-shadow: 0 0 0 2px rgba(0,0,0,0.1);
  }

  .email-input-wrapper {
    position: relative;
  }

  .email-checking,
  .email-exists,
  .email-new {
    font-size: 0.75rem;
    margin-top: 0.3rem;
    display: block;
  }

  .continue-button {
    width: 100%;
    padding: 1rem;
    font-size: 0.9rem;
    font-weight: 700;
    letter-spacing: 1px;
    margin-top: 1rem;
    border-radius: 4px;
  }

  .appointment-summary {
    margin-bottom: 1rem;
  }

  .appointment-card {
    padding: 1rem;
    border-radius: 6px;
  }

  .appointment-details {
    width: 100%;
  }

  .appointment-details h4 {
    font-size: 1rem;
    margin-bottom: 0.5rem;
  }

  .appointment-price {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
  }

  .appointment-datetime {
    font-size: 0.85rem;
    line-height: 1.4;
    color: #666;
  }

  .addon-line {
    font-size: 0.8rem;
    margin-top: 0.3rem;
    color: #666;
  }

  .close-button {
    top: 0.5rem;
    right: 0.5rem;
    width: 24px;
    height: 24px;
    font-size: 1rem;
  }
}

/* Authentication Styles */
.auth-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  padding: 2rem;
}

.auth-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 10px 30px rgba(0,0,0,0.1);
  padding: 3rem;
  width: 100%;
  max-width: 500px;
}

.auth-header {
  text-align: center;
  margin-bottom: 2rem;
}

.auth-header h2 {
  font-size: 2rem;
  font-weight: 600;
  color: #333;
  margin: 0 0 0.5rem 0;
}

.auth-header p {
  color: #666;
  margin: 0;
}

.demo-credentials {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 1.5rem;
  margin-bottom: 2rem;
  border: 1px solid #e9ecef;
}

.demo-credentials h4 {
  margin: 0 0 1rem 0;
  color: #333;
  font-size: 1rem;
}

.demo-buttons {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.demo-button {
  background: white;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  padding: 1rem;
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: center;
  font-size: 0.9rem;
  font-weight: 600;
}

.demo-button:hover {
  border-color: #007bff;
  background: #f8f9ff;
}

.demo-button.user-demo:hover {
  border-color: #28a745;
  background: #f8fff8;
}

.demo-button.admin-demo:hover {
  border-color: #dc3545;
  background: #fff8f8;
}

.demo-button small {
  display: block;
  font-size: 0.75rem;
  color: #666;
  font-weight: normal;
  margin-top: 0.25rem;
}

.auth-form {
  margin-bottom: 2rem;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-group label {
  display: block;
  font-size: 0.9rem;
  font-weight: 600;
  color: #333;
  margin-bottom: 0.5rem;
}

.form-group input {
  width: 100%;
  padding: 0.75rem;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  font-size: 1rem;
  transition: border-color 0.2s ease;
}

.form-group input:focus {
  outline: none;
  border-color: #007bff;
}

.error-message {
  background: #f8d7da;
  color: #721c24;
  padding: 0.75rem;
  border-radius: 8px;
  margin-bottom: 1rem;
  font-size: 0.9rem;
}

.auth-submit-button {
  width: 100%;
  background: #000;
  color: white;
  border: none;
  padding: 1rem;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.auth-submit-button:hover:not(:disabled) {
  background: #333;
}

.auth-submit-button:disabled {
  background: #ccc;
  cursor: not-allowed;
}

.auth-footer {
  text-align: center;
}

.auth-footer p {
  color: #666;
  margin: 0;
}

.auth-link-button {
  background: none;
  border: none;
  color: #007bff;
  cursor: pointer;
  text-decoration: underline;
  font-size: inherit;
}

.auth-link-button:hover {
  color: #0056b3;
}

/* Loading Spinner */
.loading-spinner {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.spinner {
  width: 16px;
  height: 16px;
  border: 2px solid #f3f3f3;
  border-top: 2px solid #333;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Dashboard Styles */
.dashboard-container {
  min-height: 100vh;
  background: #f8f9fa;
}

.dashboard-container.admin {
  background: #f1f3f4;
}

.dashboard-header {
  background: white;
  border-bottom: 1px solid #e9ecef;
  padding: 1rem 0;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.dashboard-header-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.dashboard-logo h1 {
  font-size: 1.5rem;
  font-weight: 600;
  color: #333;
  margin: 0;
}

.dashboard-user-menu {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.welcome-text {
  color: #666;
  font-size: 0.9rem;
}

.logout-button {
  background: #dc3545;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  font-size: 0.9rem;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.logout-button:hover {
  background: #c82333;
}

.dashboard-main {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
}

.dashboard-content {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0,0,0,0.1);
  overflow: hidden;
}

.quick-actions {
  padding: 2rem;
  border-bottom: 1px solid #e9ecef;
}

.action-button {
  background: #007bff;
  color: white;
  border: none;
  padding: 1rem 2rem;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.3s ease;
  margin-bottom: 2rem;
}

.action-button.primary {
  background: #007bff;
}

.action-button.secondary {
  background: #6c757d;
}

.action-button:hover {
  opacity: 0.9;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
}

.stats-grid.admin-stats {
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
}

.stat-card {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 1.5rem;
  text-align: center;
  border: 1px solid #e9ecef;
}

.stat-card h3 {
  font-size: 2rem;
  font-weight: 600;
  color: #333;
  margin: 0 0 0.5rem 0;
}

.stat-card p {
  color: #666;
  margin: 0;
  font-size: 0.9rem;
}

.dashboard-tabs {
  display: flex;
  border-bottom: 1px solid #e9ecef;
  background: #f8f9fa;
}

.tab-button {
  background: none;
  border: none;
  padding: 1rem 2rem;
  font-size: 0.9rem;
  font-weight: 600;
  color: #666;
  cursor: pointer;
  transition: all 0.3s ease;
  border-bottom: 3px solid transparent;
}

.tab-button:hover {
  background: #e9ecef;
  color: #333;
}

.tab-button.active {
  color: #007bff;
  border-bottom-color: #007bff;
  background: white;
}

.tab-content {
  padding: 2rem;
}

.empty-state {
  text-align: center;
  padding: 3rem;
  color: #666;
}

.empty-state h3 {
  margin: 0 0 1rem 0;
  color: #333;
}

.empty-state p {
  margin: 0 0 2rem 0;
}

.appointments-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.appointments-list.compact .appointment-card {
  padding: 1rem;
}

.appointment-card {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 1.5rem;
  border: 1px solid #e9ecef;
  transition: box-shadow 0.3s ease;
}

.appointment-card:hover {
  box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.appointment-card.past {
  opacity: 0.8;
}

.appointment-card.admin {
  background: white;
  border: 1px solid #dee2e6;
}

.appointment-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1rem;
}

.appointment-header h4 {
  font-size: 1.1rem;
  font-weight: 600;
  color: #333;
  margin: 0;
}

.service-name {
  font-size: 0.9rem;
  color: #666;
  margin: 0.25rem 0 0 0;
}

.status-badge {
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
}

.appointment-details p {
  margin: 0.5rem 0;
  color: #666;
  font-size: 0.9rem;
}

.add-ons {
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px solid #e9ecef;
}

.add-ons ul {
  margin: 0.5rem 0 0 1rem;
  padding: 0;
}

.add-ons li {
  margin: 0.25rem 0;
  color: #666;
  font-size: 0.9rem;
}

.appointment-notes {
  margin-top: 1rem;
  padding: 1rem;
  background: #fff3cd;
  border-radius: 6px;
  border: 1px solid #ffeaa7;
  font-size: 0.9rem;
}

.payment-notice {
  margin-top: 1rem;
  padding: 1rem;
  background: #f8d7da;
  border-radius: 6px;
  border: 1px solid #f5c6cb;
  color: #721c24;
  font-size: 0.9rem;
}

/* Profile Section */
.profile-section {
  max-width: 600px;
}

.profile-card {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 2rem;
  border: 1px solid #e9ecef;
}

.profile-card h3 {
  margin: 0 0 1.5rem 0;
  color: #333;
}

.profile-info {
  margin-bottom: 2rem;
}

.info-row {
  display: flex;
  justify-content: space-between;
  padding: 0.75rem 0;
  border-bottom: 1px solid #e9ecef;
}

.info-row:last-child {
  border-bottom: none;
}

.info-row label {
  font-weight: 600;
  color: #333;
}

.info-row span {
  color: #666;
}

/* Admin Dashboard Specific Styles */
.appointment-actions {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.status-select {
  padding: 0.25rem 0.5rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 0.8rem;
}

.delete-button {
  background: #dc3545;
  color: white;
  border: none;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.8rem;
}

.delete-button:hover {
  background: #c82333;
}

.detail-row {
  display: flex;
  gap: 2rem;
  margin: 0.5rem 0;
  flex-wrap: wrap;
}

.detail-row span {
  font-size: 0.9rem;
  color: #666;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
}

.section-header h3 {
  margin: 0;
  color: #333;
}

.filters select {
  padding: 0.5rem;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 0.9rem;
}

.users-list {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 1.5rem;
}

.user-card {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 1.5rem;
  border: 1px solid #e9ecef;
}

.user-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.user-header h4 {
  margin: 0;
  color: #333;
}

.user-stats {
  background: #007bff;
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 600;
}

.user-details p {
  margin: 0.5rem 0;
  font-size: 0.9rem;
  color: #666;
}

.analytics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
}

.analytics-card {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 1.5rem;
  border: 1px solid #e9ecef;
}

.analytics-card h4 {
  margin: 0 0 1rem 0;
  color: #333;
}

.revenue-stats,
.status-breakdown,
.services-breakdown {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.revenue-item,
.status-item,
.service-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 0;
  border-bottom: 1px solid #e9ecef;
}

.revenue-item:last-child,
.status-item:last-child,
.service-item:last-child {
  border-bottom: none;
}

.status-label {
  font-weight: 600;
}

.service-name {
  font-size: 0.9rem;
  color: #333;
  flex: 1;
  margin-right: 1rem;
}

.recent-activity {
  margin-top: 2rem;
}

.recent-activity h3 {
  margin: 0 0 1rem 0;
  color: #333;
}

/* Responsive Design */
@media (max-width: 768px) {
  .auth-card {
    padding: 2rem;
    margin: 1rem;
  }

  .demo-buttons {
    grid-template-columns: 1fr;
  }

  .form-row {
    grid-template-columns: 1fr;
  }

  .dashboard-header-content {
    padding: 0 1rem;
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }

  .dashboard-main {
    padding: 1rem;
  }

  .dashboard-tabs {
    flex-wrap: wrap;
  }

  .tab-button {
    flex: 1;
    min-width: 120px;
  }

  .stats-grid {
    grid-template-columns: 1fr;
  }

  .detail-row {
    flex-direction: column;
    gap: 0.5rem;
  }

  .appointment-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }

  .users-list {
    grid-template-columns: 1fr;
  }

  .analytics-grid {
    grid-template-columns: 1fr;
  }

  .section-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }
}

/* Extra small mobile devices */
@media (max-width: 480px) {
  .header-content {
    padding: 0 1rem;
  }

  .auth-links {
    gap: 0.25rem;
  }

  .auth-link {
    font-size: 0.65rem;
    padding: 0.3rem 0.6rem;
    min-width: 60px;
    letter-spacing: 0.5px;
  }

  .business-name {
    font-size: 1.5rem;
  }

  .hero-title {
    font-size: 2rem;
  }

  .hero-subtitle {
    font-size: 1rem;
  }

  .main-content {
    padding: 1rem;
  }

  .info-sections {
    gap: 1rem;
  }

  .info-card {
    padding: 1rem;
  }

  .policies-grid {
    grid-template-columns: 1fr;
  }

  .booking-section {
    padding: 1rem;
  }

  .service-card {
    padding: 1rem;
  }

  .book-button {
    padding: 0.6rem 1rem;
    font-size: 0.8rem;
  }

  /* Keep horizontal layout on mobile for service cards */
  .service-item {
    flex-direction: row;
    padding: 1rem;
  }

  .service-info {
    margin-right: 1rem;
    margin-bottom: 0;
  }

  .book-button {
    padding: 0.5rem 1rem;
    font-size: 0.75rem;
    min-width: 60px;
  }
}

/* Landscape mobile orientation */
@media (max-width: 768px) and (orientation: landscape) {
  .header {
    padding: 0.5rem 0;
  }

  .auth-link {
    padding: 0.4rem 0.7rem;
    font-size: 0.7rem;
  }

  .hero-banner {
    padding: 1rem 0;
  }

  .hero-title {
    font-size: 2.5rem;
  }
}

/* Checkout Styles */
.checkout-container {
  max-width: 1000px;
  margin: 0 auto;
  padding: 2rem;
}

.checkout-header {
  display: flex;
  align-items: center;
  margin-bottom: 2rem;
}

.checkout-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #333;
  margin: 0;
}

.checkout-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 3rem;
}

.payment-section {
  background: white;
  border-radius: 8px;
  padding: 2rem;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
  height: fit-content;
}

.payment-section h3 {
  font-size: 1rem;
  font-weight: 600;
  color: #333;
  margin: 0 0 2rem 0;
}

.payment-info h4 {
  font-size: 1.1rem;
  font-weight: 600;
  color: #333;
  margin: 0 0 1.5rem 0;
}

.payment-method-card {
  border: 1px solid #ddd;
  border-radius: 8px;
  padding: 1.5rem;
  margin-bottom: 2rem;
  background: #f9f9f9;
}

.payment-method-info span {
  font-size: 1rem;
  font-weight: 600;
  color: #333;
}

.payment-note {
  font-size: 0.9rem;
  color: #666;
  margin: 0.5rem 0 0 0;
}

.payment-button {
  background: #000;
  color: white;
  border: none;
  padding: 1rem 2rem;
  border-radius: 4px;
  font-size: 0.9rem;
  font-weight: 600;
  letter-spacing: 1px;
  cursor: pointer;
  transition: background-color 0.3s ease;
  width: 100%;
}

.payment-button:hover {
  background: #333;
}

.payment-options-section {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.payment-divider {
  text-align: center;
  position: relative;
  margin: 1rem 0;
}

.payment-divider::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  height: 1px;
  background: #e9ecef;
  z-index: 1;
}

.payment-divider span {
  background: white;
  padding: 0 1rem;
  color: #666;
  font-size: 0.9rem;
  font-weight: 600;
  position: relative;
  z-index: 2;
}

.upload-section {
  margin-top: 1rem;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.file-upload-wrapper {
  position: relative;
}

.file-input {
  position: absolute;
  opacity: 0;
  width: 0;
  height: 0;
}

.file-upload-label {
  display: inline-block;
  padding: 0.75rem 1.5rem;
  background: #f8f9fa;
  border: 2px dashed #dee2e6;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: center;
  width: 100%;
  box-sizing: border-box;
  font-size: 0.9rem;
  color: #666;
}

.file-upload-label:hover {
  border-color: #007bff;
  background: #f0f8ff;
  color: #007bff;
}

.upload-button {
  background: #28a745;
  margin-top: 0.5rem;
}

.upload-button:hover:not(:disabled) {
  background: #218838;
}

.upload-button:disabled {
  background: #6c757d;
  cursor: not-allowed;
  opacity: 0.6;
}

.order-summary {
  background: white;
  border-radius: 8px;
  padding: 2rem;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
  height: fit-content;
}

.order-summary h4 {
  font-size: 1.1rem;
  font-weight: 600;
  color: #333;
  margin: 0 0 1.5rem 0;
}

.order-details {
  border-top: 1px solid #e0e0e0;
  padding-top: 1.5rem;
}

.service-line {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 0.5rem;
}

.service-name {
  font-size: 1rem;
  font-weight: 600;
  color: #333;
  flex: 1;
}

.service-price {
  font-size: 1rem;
  font-weight: 600;
  color: #333;
}

.service-date {
  font-size: 0.9rem;
  color: #666;
  margin-bottom: 1rem;
}

.addon-line {
  display: flex;
  justify-content: space-between;
  font-size: 0.9rem;
  color: #666;
  margin: 0.25rem 0;
}

.coupon-section {
  margin: 1.5rem 0;
  padding: 1rem 0;
  border-top: 1px solid #e0e0e0;
  border-bottom: 1px solid #e0e0e0;
}

.coupon-toggle {
  background: none;
  border: none;
  color: #666;
  font-size: 0.9rem;
  cursor: pointer;
  text-align: left;
  width: 100%;
}

.coupon-input {
  display: flex;
  gap: 0.5rem;
  margin-top: 1rem;
}

.coupon-input input {
  flex: 1;
  padding: 0.5rem;
  border: 1px solid #ddd;
  border-radius: 4px;
}

.coupon-input button {
  background: #000;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 4px;
  cursor: pointer;
}

.pricing-breakdown {
  margin-top: 1.5rem;
}

.subtotal-line {
  display: flex;
  justify-content: space-between;
  font-size: 1rem;
  color: #333;
  margin-bottom: 1rem;
}

.payment-options {
  margin: 1rem 0;
}

.payment-option {
  display: flex;
  align-items: center;
  margin-bottom: 0.75rem;
}

.payment-option input[type="radio"] {
  margin-right: 0.75rem;
}

.payment-option label {
  display: flex;
  justify-content: space-between;
  width: 100%;
  cursor: pointer;
  font-size: 0.95rem;
}

.option-label {
  color: #333;
}

.option-price {
  font-weight: 600;
  color: #333;
}

.balance-due {
  display: flex;
  justify-content: space-between;
  font-size: 0.9rem;
  color: #666;
  margin-top: 0.5rem;
  padding-top: 0.5rem;
  border-top: 1px solid #e0e0e0;
}

/* Responsive for Checkout */
@media (max-width: 768px) {
  .checkout-content {
    grid-template-columns: 1fr;
    gap: 2rem;
  }

  .checkout-container {
    padding: 1rem;
  }

  .payment-section,
  .order-summary {
    padding: 1.5rem;
  }

  .payment-options-section {
    gap: 1rem;
  }

  .upload-section {
    gap: 0.75rem;
  }

  .file-upload-label {
    padding: 0.6rem 1rem;
    font-size: 0.8rem;
  }
}

/* Header Navigation */
.header-nav {
  display: flex;
  align-items: center;
  margin: 0 2rem;
}

.nav-links {
  display: flex;
  gap: 1.5rem;
}

.nav-link {
  color: #2c5530;
  text-decoration: none;
  font-weight: 600;
  font-size: 0.9rem;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  transition: all 0.3s ease;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.nav-link:hover {
  background: #f8f9fa;
  color: #d4af37;
}

/* Page Layouts */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 2px solid #d4af37;
}

.page-header h1 {
  color: #2c5530;
  font-size: 2.5rem;
  margin: 0;
}

.header-actions {
  display: flex;
  gap: 1rem;
  align-items: center;
}

.search-input {
  padding: 0.5rem 1rem;
  border: 2px solid #d4af37;
  border-radius: 6px;
  font-size: 1rem;
  min-width: 250px;
}

.sort-select {
  padding: 0.5rem 1rem;
  border: 2px solid #d4af37;
  border-radius: 6px;
  font-size: 1rem;
  background: white;
}

/* Buttons */
.btn-primary {
  background: #2c5530;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 6px;
  font-weight: 600;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.btn-primary:hover {
  background: #1e3a22;
  transform: translateY(-2px);
}

.btn-secondary {
  background: #d4af37;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 4px;
  font-weight: 600;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-secondary:hover {
  background: #b8941f;
}

.btn-danger {
  background: #dc3545;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 4px;
  font-weight: 600;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-danger:hover {
  background: #c82333;
}

/* Loading and Empty States */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
  font-size: 1.2rem;
  color: #666;
}

.empty-state {
  text-align: center;
  padding: 3rem;
  color: #666;
  font-size: 1.1rem;
}

/* Filter Tabs */
.filter-tabs {
  display: flex;
  gap: 1rem;
  margin-bottom: 2rem;
  border-bottom: 2px solid #e0e0e0;
}

.filter-tab {
  background: none;
  border: none;
  padding: 1rem 1.5rem;
  font-size: 1rem;
  font-weight: 600;
  color: #666;
  cursor: pointer;
  border-bottom: 3px solid transparent;
  transition: all 0.3s ease;
}

.filter-tab:hover {
  color: #2c5530;
}

.filter-tab.active {
  color: #2c5530;
  border-bottom-color: #d4af37;
}

/* Settings Tabs */
.settings-tabs {
  display: flex;
  gap: 1rem;
  margin-bottom: 2rem;
  border-bottom: 2px solid #e0e0e0;
}

.settings-tab {
  background: none;
  border: none;
  padding: 1rem 1.5rem;
  font-size: 1rem;
  font-weight: 600;
  color: #666;
  cursor: pointer;
  border-bottom: 3px solid transparent;
  transition: all 0.3s ease;
}

.settings-tab:hover {
  color: #2c5530;
}

.settings-tab.active {
  color: #2c5530;
  border-bottom-color: #d4af37;
}

/* Settings Form */
.settings-form {
  max-width: 600px;
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 600;
  color: #333;
}

.form-group input,
.form-group textarea {
  width: 100%;
  padding: 0.75rem;
  border: 2px solid #d4af37;
  border-radius: 6px;
  font-size: 1rem;
  font-family: inherit;
}

.form-group textarea {
  resize: vertical;
  min-height: 100px;
}

/* Working Hours Form */
.working-hours-form {
  max-width: 600px;
}

.day-hours {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem;
  border-bottom: 1px solid #e0e0e0;
}

.day-name {
  font-weight: 600;
  color: #333;
  min-width: 120px;
}

.hours-controls {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.hours-controls label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 600;
}

.hours-controls input[type="time"] {
  padding: 0.5rem;
  border: 2px solid #d4af37;
  border-radius: 4px;
  font-size: 0.9rem;
}

.hours-controls span {
  color: #666;
  font-weight: 600;
}

/* Mobile Price List Improvements */
@media (max-width: 768px) {
  .retie-item {
    flex-direction: column;
    align-items: stretch;
    padding: 1rem;
    gap: 0.5rem;
  }

  .retie-item .retie-duration {
    font-size: 1.1rem;
    font-weight: 600;
    text-align: center;
  }

  .retie-item .retie-dots {
    display: none;
  }

  .retie-item .retie-price {
    font-size: 1.3rem;
    font-weight: 700;
    text-align: center;
    color: #d4af37;
  }

  .retie-book-btn {
    width: 100%;
    padding: 0.75rem;
    font-size: 1rem;
    margin: 0;
  }

  .other-services .service-item {
    flex-direction: column;
    align-items: stretch;
    gap: 0.5rem;
  }

  .other-services .service-info {
    flex-direction: column;
    text-align: center;
    gap: 0.25rem;
  }

  .other-services .service-dots {
    display: none;
  }

  .other-services .service-name {
    font-size: 1.1rem;
  }

  .other-services .service-price {
    font-size: 1.2rem;
    color: #d4af37;
  }

  /* Header alignment on mobile */
  .header-content {
    flex-direction: column;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    justify-content: center;
  }

  .header-left {
    order: 1;
  }

  .logo-container {
    justify-content: center;
    text-align: center;
  }

  .business-name-header {
    text-align: center;
    font-size: 1.3rem;
  }

  .header-nav {
    order: 2;
    margin: 0;
  }

  .auth-links {
    order: 3;
    display: flex;
    gap: 1rem;
    justify-content: center;
  }

  .auth-link {
    padding: 0.5rem 1rem;
    font-size: 0.9rem;
  }
}

/* React DatePicker Styling */
.datepicker-wrapper {
  display: flex;
  justify-content: center;
  margin: 2rem 0;
}

.react-datepicker {
  border: 2px solid #d4af37;
  border-radius: 12px;
  box-shadow: 0 8px 25px rgba(0,0,0,0.15);
  font-family: 'Alegreya', Helvetica, sans-serif;
}

.react-datepicker__header {
  background: linear-gradient(135deg, #d4af37 0%, #b8860b 100%);
  border-bottom: 2px solid #d4af37;
  border-radius: 10px 10px 0 0;
  padding: 1rem;
}

.react-datepicker__current-month {
  color: white;
  font-size: 1.2rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.react-datepicker__navigation {
  top: 1rem;
}

.react-datepicker__navigation--previous {
  left: 1rem;
}

.react-datepicker__navigation--next {
  right: 1rem;
}

.react-datepicker__navigation-icon::before {
  border-color: white;
  border-width: 2px 2px 0 0;
}

.react-datepicker__day-names {
  background: #f8f9fa;
  border-bottom: 1px solid #e0e0e0;
}

.react-datepicker__day-name {
  color: #666;
  font-weight: 600;
  font-size: 0.9rem;
  padding: 0.75rem;
}

.react-datepicker__day {
  color: #333;
  font-weight: 500;
  border-radius: 50%;
  margin: 0.2rem;
  padding: 0.5rem;
  transition: all 0.3s ease;
}

.react-datepicker__day:hover {
  background: #f0f8ff;
  color: #2c5530;
}

.react-datepicker__day--selected {
  background: #2c5530 !important;
  color: white !important;
  font-weight: 600;
}

.react-datepicker__day--disabled {
  color: #ccc !important;
  cursor: not-allowed !important;
  background: #f5f5f5 !important;
}

.react-datepicker__day--outside-month {
  color: #ccc;
}

.react-datepicker__day--weekend {
  color: #dc3545;
}

.react-datepicker__day--today {
  background: #fff3cd;
  color: #856404;
  font-weight: 600;
}

.time-slot.selected {
  background: #2c5530;
  color: white;
  font-weight: 600;
}

/* Back Button Styling */
.back-button {
  background: none;
  border: 2px solid #2c5530;
  color: #2c5530;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  font-weight: 600;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.3s ease;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.back-button:hover {
  background: #2c5530;
  color: white;
  transform: translateY(-2px);
}

.booking-header {
  display: flex;
  align-items: center;
  gap: 2rem;
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 2px solid #d4af37;
}

.booking-header h1 {
  color: #2c5530;
  font-size: 2rem;
  margin: 0;
}

.datetime-header {
  display: flex;
  align-items: center;
  gap: 2rem;
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 2px solid #d4af37;
}

.datetime-title {
  color: #2c5530;
  font-size: 2rem;
  margin: 0;
}

.checkout-header {
  display: flex;
  align-items: center;
  gap: 2rem;
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 2px solid #d4af37;
}

/* Service Reviews Styles */
.service-reviews-summary {
  margin: 1rem 0;
  padding: 0.75rem;
  background: #f8f9fa;
  border-radius: 6px;
  border-left: 3px solid #d4af37;
}

.rating-summary {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
}

.service-rating {
  display: flex;
  gap: 2px;
}

.service-rating .star {
  color: #ddd;
  font-size: 0.9rem;
  transition: color 0.2s ease;
}

.service-rating .star.filled {
  color: #ffc107;
}

.rating-text {
  font-size: 0.8rem;
  color: #666;
  font-weight: 500;
}

.recent-review {
  margin-top: 0.5rem;
}

.review-comment {
  font-size: 0.8rem;
  color: #555;
  font-style: italic;
  margin: 0 0 0.25rem 0;
  line-height: 1.3;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.review-author {
  font-size: 0.7rem;
  color: #888;
  font-weight: 500;
}

/* Service Reviews Styles */
.service-reviews-summary {
  margin: 1rem 0;
  padding: 0.75rem;
  background: #f8f9fa;
  border-radius: 6px;
  border-left: 3px solid #d4af37;
}

.rating-summary {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
}

.service-rating {
  display: flex;
  gap: 2px;
}

.service-rating .star {
  color: #ddd;
  font-size: 0.9rem;
  transition: color 0.2s ease;
}

.service-rating .star.filled {
  color: #ffc107;
}

.rating-text {
  font-size: 0.8rem;
  color: #666;
  font-weight: 500;
}

.recent-review {
  margin-top: 0.5rem;
}

.review-comment {
  font-size: 0.8rem;
  color: #555;
  font-style: italic;
  margin: 0 0 0.25rem 0;
  line-height: 1.3;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.review-author {
  font-size: 0.7rem;
  color: #888;
  font-weight: 500;
}

/* Services Grid Layout */
.services-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.service-card {
  background: white;
  border-radius: 8px;
  padding: 1.5rem;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  border: 1px solid #e0e0e0;
}

.service-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.service-category-section {
  margin-bottom: 3rem;
}

/* Reviews Section in DateTimeSelection */
.reviews-section {
  margin-top: 3rem;
  padding-top: 2rem;
  border-top: 1px solid #e0e0e0;
}

.reviews-section h3 {
  margin-bottom: 1.5rem;
  color: #333;
  font-size: 1.5rem;
  font-weight: 600;
}

/* Service Reviews Component */
.service-reviews-container {
  background: white;
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 4px 6px rgba(0,0,0,0.1);
  border: 1px solid #e0e0e0;
  margin: 1rem 0;
}

.service-reviews-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid #f0f0f0;
}

.service-reviews-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #333;
  margin: 0;
}

.write-review-btn {
  background: linear-gradient(135deg, #2c5530 0%, #1e3a21 100%);
  color: white;
  border: none;
  padding: 0.875rem 2rem;
  border-radius: 10px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.95rem;
  box-shadow: 0 4px 15px rgba(44, 85, 48, 0.3);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.write-review-btn:hover {
  background: linear-gradient(135deg, #1e3a21 0%, #0f1d10 100%);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(44, 85, 48, 0.4);
}

.reviews-summary {
  display: flex;
  align-items: center;
  gap: 2rem;
  margin-bottom: 2rem;
  padding: 1.5rem;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.average-rating {
  text-align: center;
}

.rating-number {
  font-size: 2.5rem;
  font-weight: bold;
  color: #333;
  margin: 0;
}

.rating-stars {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  margin: 0.5rem 0;
}

.star {
  color: #ffc107;
  font-size: 1.2rem;
}

.star.empty {
  color: #e9ecef;
}

.rating-count {
  color: #666;
  font-size: 0.9rem;
}

.rating-distribution {
  flex: 1;
}

.rating-bar {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 0.5rem;
}

.rating-bar-label {
  font-size: 0.85rem;
  color: #666;
  min-width: 60px;
}

.rating-bar-track {
  flex: 1;
  height: 8px;
  background: #e9ecef;
  border-radius: 4px;
  overflow: hidden;
}

.rating-bar-fill {
  height: 100%;
  background: #ffc107;
  transition: width 0.3s ease;
}

.rating-bar-count {
  font-size: 0.85rem;
  color: #666;
  min-width: 30px;
  text-align: right;
}

.review-form-container {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 12px;
  padding: 2rem;
  margin-bottom: 2rem;
}

.review-form-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #333;
  margin-bottom: 1.5rem;
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-label {
  display: block;
  font-weight: 500;
  color: #333;
  margin-bottom: 0.5rem;
  font-size: 0.9rem;
}

.form-input {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #ddd;
  border-radius: 8px;
  font-size: 1rem;
  transition: border-color 0.2s ease;
  background: white;
}

.form-input:focus {
  outline: none;
  border-color: #2c5530;
  box-shadow: 0 0 0 3px rgba(44, 85, 48, 0.1);
}

.form-textarea {
  resize: vertical;
  min-height: 100px;
}

.interactive-stars {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
}

.interactive-star {
  font-size: 1.8rem;
  cursor: pointer;
  transition: color 0.2s ease;
  color: #e9ecef;
}

.interactive-star.filled {
  color: #ffc107;
}

.interactive-star:hover {
  color: #ffc107;
}

.rating-label {
  font-size: 0.9rem;
  color: #666;
  margin-left: 0.5rem;
}

.form-actions {
  display: flex;
  gap: 1rem;
  margin-top: 1.5rem;
}

.btn-submit {
  background: #2c5530;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.btn-submit:hover:not(:disabled) {
  background: #1e3a21;
  transform: translateY(-1px);
}

.btn-submit:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-cancel {
  background: #6c757d;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.btn-cancel:hover {
  background: #5a6268;
}

.reviews-list {
  margin-top: 2rem;
}

.review-item {
  background: white;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 1.5rem;
  margin-bottom: 1rem;
  transition: box-shadow 0.2s ease;
}

.review-item:hover {
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.review-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1rem;
}

.review-author {
  font-weight: 600;
  color: #333;
  margin-bottom: 0.25rem;
}

.review-date {
  font-size: 0.85rem;
  color: #666;
}

.review-rating {
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.review-title {
  font-weight: 600;
  color: #333;
  margin-bottom: 0.5rem;
  font-size: 1.1rem;
}

.review-comment {
  color: #555;
  line-height: 1.6;
  margin-bottom: 1rem;
}

.verified-badge {
  display: inline-flex;
  align-items: center;
  gap: 0.25rem;
  background: #e3f2fd;
  color: #1976d2;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: 500;
}

.no-reviews-state {
  text-align: center;
  padding: 4rem 2rem;
  color: #666;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 12px;
  border: 2px dashed #dee2e6;
}

.no-reviews-icon {
  font-size: 4rem;
  color: #2c5530;
  margin-bottom: 1.5rem;
  opacity: 0.7;
}

.no-reviews-title {
  font-size: 1.3rem;
  font-weight: 600;
  color: #2c5530;
  margin-bottom: 0.75rem;
}

.no-reviews-subtitle {
  font-size: 1rem;
  color: #6c757d;
  line-height: 1.5;
}

.pagination-container {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 1rem;
  margin-top: 2rem;
  padding-top: 1.5rem;
  border-top: 1px solid #e9ecef;
}

.pagination-btn {
  background: white;
  border: 1px solid #ddd;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 0.9rem;
}

.pagination-btn:hover:not(:disabled) {
  background: #f8f9fa;
  border-color: #2c5530;
}

.pagination-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.pagination-info {
  font-size: 0.9rem;
  color: #666;
}

/* Reviews Page Styling */
.dashboard-header {
  margin-bottom: 2rem;
  padding-bottom: 1.5rem;
  border-bottom: 2px solid #e9ecef;
}

.dashboard-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: #2c5530;
  margin-bottom: 0.5rem;
}

.dashboard-subtitle {
  font-size: 1.1rem;
  color: #6c757d;
  margin: 0;
}

.dashboard-section {
  margin-bottom: 3rem;
}

.section-title {
  font-size: 1.75rem;
  font-weight: 600;
  color: #333;
  margin-bottom: 0.5rem;
}

.section-subtitle {
  font-size: 1rem;
  color: #6c757d;
  margin-bottom: 1.5rem;
}

.appointments-grid {
  display: grid;
  gap: 1.5rem;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
}

.appointment-card {
  background: white;
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 4px 15px rgba(0,0,0,0.1);
  border: 1px solid #e9ecef;
  transition: all 0.3s ease;
}

.appointment-card:hover {
  box-shadow: 0 8px 25px rgba(0,0,0,0.15);
  transform: translateY(-2px);
}

.appointment-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1rem;
}

.appointment-service {
  font-size: 1.25rem;
  font-weight: 600;
  color: #2c5530;
  margin: 0;
}

.appointment-date {
  font-size: 0.9rem;
  color: #6c757d;
  background: #f8f9fa;
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
}

.appointment-details {
  margin-bottom: 1.5rem;
}

.service-category {
  font-size: 0.9rem;
  color: #2c5530;
  font-weight: 500;
  margin-bottom: 0.25rem;
}

.appointment-time {
  font-size: 0.85rem;
  color: #6c757d;
  margin: 0;
}

.appointment-actions {
  text-align: center;
}

.reviews-grid {
  display: grid;
  gap: 1.5rem;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
}

.review-card {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 4px 15px rgba(0,0,0,0.1);
  border: 1px solid #e9ecef;
  transition: all 0.3s ease;
}

.review-card:hover {
  box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.review-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1rem;
}

.review-service-info {
  flex: 1;
}

.review-service-name {
  font-size: 1.1rem;
  font-weight: 600;
  color: #2c5530;
  margin-bottom: 0.25rem;
}

.review-date {
  font-size: 0.85rem;
  color: #6c757d;
}

.review-status {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 0.5rem;
}

.status-badge {
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 500;
  text-transform: uppercase;
}

.status-badge.pending {
  background: #fff3cd;
  color: #856404;
}

.status-badge.approved {
  background: #d1edff;
  color: #0c5460;
}

.status-badge.rejected {
  background: #f8d7da;
  color: #721c24;
}

.review-title {
  font-size: 1rem;
  font-weight: 600;
  color: #333;
  margin-bottom: 0.75rem;
}

.review-comment {
  color: #555;
  line-height: 1.6;
  margin-bottom: 1rem;
}

.admin-response {
  background: #e3f2fd;
  border-left: 4px solid #2196f3;
  padding: 1rem;
  border-radius: 0 8px 8px 0;
  margin-top: 1rem;
}

.admin-response-label {
  font-size: 0.85rem;
  font-weight: 600;
  color: #1976d2;
  margin-bottom: 0.5rem;
}

.admin-response-text {
  font-size: 0.9rem;
  color: #1565c0;
  line-height: 1.5;
}

.empty-state {
  text-align: center;
  padding: 4rem 2rem;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(0,0,0,0.1);
  border: 2px dashed #dee2e6;
}

.empty-state-icon {
  font-size: 4rem;
  color: #2c5530;
  margin-bottom: 1.5rem;
  opacity: 0.7;
}

.empty-state-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #2c5530;
  margin-bottom: 0.75rem;
}

.empty-state-subtitle {
  font-size: 1rem;
  color: #6c757d;
  line-height: 1.5;
}

/* Responsive Design */
@media (max-width: 768px) {
  .service-reviews-container {
    padding: 1.5rem;
    margin: 0.5rem 0;
  }

  .service-reviews-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }

  .reviews-summary {
    flex-direction: column;
    text-align: center;
    gap: 1.5rem;
  }

  .form-actions {
    flex-direction: column;
  }

  .btn-submit,
  .btn-cancel {
    width: 100%;
  }

  .appointments-grid,
  .reviews-grid {
    grid-template-columns: 1fr;
  }

  .appointment-header,
  .review-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.75rem;
  }

  .review-status {
    flex-direction: row;
    align-items: center;
  }

  .dashboard-title {
    font-size: 2rem;
  }
}

/* Service Description Styles for Appointment Cards */
.appointment-card .service-description {
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px solid #e0e0e0;
}

.appointment-card .service-description h5 {
  font-size: 0.9rem;
  font-weight: 600;
  color: #333;
  margin-bottom: 0.5rem;
}

.appointment-card .description-content {
  font-size: 0.85rem;
  color: #666;
  line-height: 1.4;
  max-height: 200px;
  overflow-y: auto;
}

.appointment-card .description-content p {
  margin-bottom: 0.5rem;
}

.appointment-card .appointment-duration {
  font-size: 0.9rem;
  color: #666;
  margin-bottom: 0.5rem;
}

/* Service Description Styles for Appointment Cards */
.appointment-card .service-description {
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px solid #e0e0e0;
}

.appointment-card .service-description h5 {
  font-size: 0.9rem;
  font-weight: 600;
  color: #333;
  margin-bottom: 0.5rem;
}

.appointment-card .description-content {
  font-size: 0.9rem;
  color: #666;
  line-height: 1.6;
  max-height: none;
  overflow-y: visible;
  padding: 1rem;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
  margin-top: 0.5rem;
}

.appointment-card .description-content p {
  margin-bottom: 0.75rem;
  line-height: 1.6;
}

.appointment-card .description-content p:last-child {
  margin-bottom: 0;
}

.appointment-card .appointment-duration {
  font-size: 0.9rem;
  color: #666;
  margin-bottom: 0.5rem;
}

/* Service Image Gallery Styles */
.service-gallery-container {
  margin-top: 1.5rem;
  padding-top: 1.5rem;
  border-top: 2px solid #e3f2fd;
}

.gallery-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: #333;
  margin-bottom: 1rem;
  text-align: center;
  padding: 0.75rem;
  background: linear-gradient(135deg, #e3f2fd 0%, #f8f9fa 100%);
  border-radius: 8px;
  border: 1px solid #e3f2fd;
}

.gallery-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 1rem;
  margin-bottom: 1rem;
}

.gallery-thumbnail {
  position: relative;
  border-radius: 12px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  background: white;
}

.gallery-thumbnail:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.15);
}

.gallery-thumbnail.main-image {
  border: 3px solid #3b82f6;
  box-shadow: 0 4px 8px rgba(59, 130, 246, 0.2);
}

.gallery-thumbnail.main-image:hover {
  box-shadow: 0 8px 16px rgba(59, 130, 246, 0.3);
}

.thumbnail-image {
  width: 100%;
  height: 150px;
  object-fit: cover;
  display: block;
  transition: transform 0.3s ease;
}

.gallery-thumbnail:hover .thumbnail-image {
  transform: scale(1.05);
}

.main-badge {
  position: absolute;
  top: 8px;
  left: 8px;
  background: #3b82f6;
  color: white;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 0.7rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.image-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.gallery-thumbnail:hover .image-overlay {
  opacity: 1;
}

.view-text {
  color: white;
  font-size: 1rem;
  font-weight: 600;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
}

/* Service Images Inside Appointment Card */
.service-images-in-card {
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px solid #e9ecef;
}

.service-images-in-card h5 {
  font-size: 1rem;
  font-weight: 600;
  color: #333;
  margin-bottom: 0.75rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.service-images-in-card h5::before {
  content: "🎨";
  font-size: 1.1rem;
}

.service-images-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
  gap: 0.75rem;
  margin-bottom: 0.75rem;
}

.service-image-card {
  position: relative;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  background: white;
}

.service-image-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.15);
}

.service-image-card.main-style {
  border: 2px solid #3b82f6;
  box-shadow: 0 2px 6px rgba(59, 130, 246, 0.2);
}

.service-image-card.main-style:hover {
  box-shadow: 0 4px 10px rgba(59, 130, 246, 0.3);
}

.service-image-in-card {
  width: 100%;
  height: 120px;
  object-fit: cover;
  display: block;
  transition: transform 0.3s ease;
}

.service-image-card:hover .service-image-in-card {
  transform: scale(1.05);
}

.image-caption {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
  color: white;
  padding: 0.5rem 0.25rem 0.25rem;
  font-size: 0.75rem;
  font-weight: 600;
  text-align: center;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

.style-count {
  text-align: center;
  font-size: 0.85rem;
  color: #666;
  font-style: italic;
  padding: 0.5rem;
  background: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e9ecef;
}

/* Microloc Information Section */
.microloc-info-section {
  background: white;
  border-radius: 8px;
  padding: 0.75rem;
  margin-bottom: 2rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  border: 2px solid #e3f2fd;
  max-width: 100%;
  width: 100%;
}

.microloc-image-container {
  text-align: center;
  margin-bottom: 1rem;
}

.microloc-image {
  max-width: 100%;
  height: auto;
  max-height: 300px;
  border-radius: 8px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  object-fit: cover;
}

.microloc-notice {
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 6px;
  padding: 1rem;
  text-align: center;
}

.microloc-notice h4 {
  color: #495057;
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.microloc-notice p {
  color: #6c757d;
  font-size: 0.95rem;
  line-height: 1.5;
  margin: 0;
}

/* Responsive adjustments for microloc section */
@media (max-width: 768px) {
  .microloc-info-section {
    padding: 1rem;
    margin-bottom: 1.5rem;
  }

  .microloc-image {
    max-height: 350px;
    min-height: 300px;
  }

  .microloc-notice h4 {
    font-size: 1.1rem;
  }

  .microloc-notice p {
    font-size: 1rem;
  }
}

/* Extra small mobile devices */
@media (max-width: 480px) {
  .microloc-info-section {
    padding: 0.25rem;
    margin: 0 -0.75rem 1.5rem -0.75rem;
    border-radius: 4px;
  }

  .microloc-image-container {
    margin-bottom: 0.125rem;
  }

  .microloc-image {
    min-height: 320px;
    max-height: 400px;
    border-radius: 8px;
  }

  .microloc-notice {
    padding: 0.5rem;
    border-radius: 6px;
  }

  .microloc-notice h4 {
    font-size: 1.1rem;
    margin-bottom: 0.125rem;
  }

  .microloc-notice p {
    font-size: 1rem;
    line-height: 1.4;
  }
}

/* Service Images Gallery */
.service-images {
  padding-top: 1rem;
  border-top: 1px solid #e0e0e0;
  margin-top: 1rem;
}

.service-images h5 {
  font-size: 0.9rem;
  font-weight: 600;
  color: #333;
  margin-bottom: 0.5rem;
}

.service-images-gallery {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.service-gallery-image {
  width: 100%;
  height: 80px;
  object-fit: cover;
  border-radius: 6px;
  border: 1px solid #e0e0e0;
  cursor: pointer;
  transition: transform 0.2s ease;
}

.service-gallery-image:hover {
  transform: scale(1.05);
  border-color: #3b82f6;
}

/* Responsive adjustments for service images */
@media (max-width: 768px) {
  .service-images {
    padding: 0.75rem;
    margin-top: 0.75rem;
  }

  .service-images h5 {
    font-size: 0.95rem;
    margin-bottom: 0.5rem;
  }

  .service-images-gallery {
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 0.5rem;
    margin-bottom: 0.5rem;
  }

  .service-gallery-image {
    height: 100px;
  }

  .image-label {
    font-size: 0.65rem;
    padding: 1px 4px;
  }

  .image-count {
    font-size: 0.8rem;
    padding: 0.2rem;
  }
}

@media (max-width: 480px) {
  .service-images {
    padding: 0.5rem;
    margin-top: 0.5rem;
  }

  .service-images-gallery {
    grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
    gap: 0.4rem;
  }

  .service-gallery-image {
    height: 80px;
  }

  .service-images h5 {
    font-size: 0.9rem;
  }
}

/* Service Card Images */
.service-card-images {
  position: relative;
  overflow: hidden;
  border-radius: 8px 8px 0 0;
}

.main-service-image {
  position: relative;
  overflow: hidden;
}

.image-count-badge {
  position: absolute;
  top: 8px;
  right: 8px;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 600;
  backdrop-filter: blur(4px);
}

.additional-images-preview {
  position: absolute;
  bottom: 8px;
  right: 8px;
  display: flex;
  gap: 4px;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.service-card-images:hover .additional-images-preview {
  opacity: 1;
}

.preview-image {
  width: 40px;
  height: 40px;
  object-fit: cover;
  border-radius: 6px;
  border: 2px solid white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  transition: transform 0.2s ease;
}

.preview-image:hover {
  transform: scale(1.1);
}

/* Responsive adjustments for service card images */
@media (max-width: 768px) {
  .image-count-badge {
    font-size: 0.7rem;
    padding: 3px 6px;
    top: 6px;
    right: 6px;
  }

  .additional-images-preview {
    bottom: 6px;
    right: 6px;
    gap: 3px;
  }

  .preview-image {
    width: 32px;
    height: 32px;
    border-width: 1px;
  }

  /* Service Images Section - Mobile */
  .service-images-section {
    padding: 1rem;
    margin-bottom: 1.5rem;
  }

  .service-images-section h4 {
    font-size: 1.1rem;
    margin-bottom: 0.75rem;
  }

  .service-images-gallery-main {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 0.75rem;
  }

  .service-gallery-image-main {
    height: 150px;
    border-radius: 8px;
  }

  .image-count-main {
    font-size: 0.9rem;
    padding: 0.5rem;
  }
}

@media (max-width: 480px) {
  .service-images-section {
    padding: 0.75rem;
    margin-bottom: 1rem;
  }

  .service-images-section h4 {
    font-size: 1rem;
    margin-bottom: 0.5rem;
  }

  .service-images-gallery-main {
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 0.5rem;
  }

  .service-gallery-image-main {
    height: 120px;
    border-radius: 6px;
  }

  .image-count-main {
    font-size: 0.85rem;
    padding: 0.4rem;
  }
}

/* Service Image Gallery Styles */
.service-gallery-container {
  margin-top: 1.5rem;
  padding-top: 1.5rem;
  border-top: 2px solid #e3f2fd;
}

.gallery-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: #333;
  margin-bottom: 1rem;
  text-align: center;
  padding: 0.75rem;
  background: linear-gradient(135deg, #e3f2fd 0%, #f8f9fa 100%);
  border-radius: 8px;
  border: 1px solid #e3f2fd;
}

.gallery-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 1rem;
  margin-bottom: 1rem;
}

.gallery-thumbnail {
  position: relative;
  border-radius: 12px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  background: white;
}

.gallery-thumbnail:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.15);
}

.gallery-thumbnail.main-image {
  border: 3px solid #3b82f6;
  box-shadow: 0 4px 8px rgba(59, 130, 246, 0.2);
}

.gallery-thumbnail.main-image:hover {
  box-shadow: 0 8px 16px rgba(59, 130, 246, 0.3);
}

.thumbnail-image {
  width: 100%;
  height: 150px;
  object-fit: cover;
  display: block;
  transition: transform 0.3s ease;
}

.gallery-thumbnail:hover .thumbnail-image {
  transform: scale(1.05);
}

.main-badge {
  position: absolute;
  top: 8px;
  left: 8px;
  background: #3b82f6;
  color: white;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 0.7rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.image-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.gallery-thumbnail:hover .image-overlay {
  opacity: 1;
}

.view-text {
  color: white;
  font-size: 1rem;
  font-weight: 600;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
}

/* Gallery Responsive Styles */
@media (max-width: 768px) {
  .gallery-grid {
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 0.75rem;
  }

  .thumbnail-image {
    height: 120px;
  }

  .gallery-title {
    font-size: 1rem;
    padding: 0.5rem;
  }

  .main-badge {
    font-size: 0.6rem;
    padding: 3px 6px;
    top: 6px;
    left: 6px;
  }
}

@media (max-width: 480px) {
  .gallery-grid {
    grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
    gap: 0.5rem;
  }

  .thumbnail-image {
    height: 100px;
  }

  .gallery-title {
    font-size: 0.9rem;
    padding: 0.4rem;
  }

  .view-text {
    font-size: 0.85rem;
  }
}

/* Price List Section - Reduced spacing */
.price-list-card .reties-title {
  margin: 0.5rem 0 0.25rem 0 !important;
}

.price-list-card .other-services {
  margin-top: 0.25rem !important;
  padding-top: 0.25rem !important;
  border-top: 2px solid #d4af37;
}
